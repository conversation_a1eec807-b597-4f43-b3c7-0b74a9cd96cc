import json
import re
import ast
from typing import Any, Dict, List, Optional
import datetime
import argparse
# python 抖音数据的统计.py --date 2025-07-25 

import pymongo

###########################################################################
#                           数据库操作类                                   #
###########################################################################


class MongoManager:
    """负责 MongoDB 的读写操作。"""

    def __init__(self,
                 uri: str = "**************************************************",
                 db_name: str = "douyin",
                 in_coll: str = "douyin_sp_07_26",
                 out_coll: str = "douyin_sp_outcome_07_26") -> None:
        self.client = pymongo.MongoClient(uri)
        self.db = self.client[db_name]
        self.in_coll = self.db[in_coll]
        self.out_coll = self.db[out_coll]

    # ------------------------------------------------------------------ #
    def fetch_documents(self, date_str: Optional[str] = None):
        """生成器，按日期过滤返回原始文档。

        Args:
            date_str: 若提供 (YYYY-MM-DD)，仅返回 inserted_at 等于该日期的文档。
        """
        query: Dict[str, Any] = {}
        if date_str:
            query["inserted_at"] = date_str
        for doc in self.in_coll.find(query):
            yield doc

    def save_result(self, result: Dict[str, Any]) -> None:
        """将结果写入输出集合。若 aweme_id 已存在则覆盖更新。"""
        aweme_id = result.get("aweme_id")
        if aweme_id:
            self.out_coll.update_one({"aweme_id": aweme_id}, {
                "$set": result,
            }, upsert=True)
        else:
            # 没有 aweme_id，直接插入
            self.out_coll.insert_one(result)

    def close(self):
        self.client.close()

###########################################################################
#                           字符串与 JSON 清理                            #
###########################################################################

def _unescape_douyin_string(escaped: str) -> str:
    """去除抖音返回字符串中的多层转义。"""
    if not isinstance(escaped, str):
        return escaped
    # 先替换 \" -> "
    text = re.sub(r'\\+"', '"', escaped)
    # 再去掉剩余的反斜杠
    text = re.sub(r'\\', '', text)
    return text


def _recursive_clean(data: Any) -> Any:
    """递归地去除转义，并尝试解析嵌套 JSON 字符串。"""
    if isinstance(data, dict):
        return {k: _recursive_clean(v) for k, v in data.items()}
    if isinstance(data, list):
        return [_recursive_clean(i) for i in data]
    if isinstance(data, str):
        cleaned = _unescape_douyin_string(data)
        cleaned = _unescape_douyin_string(cleaned)  # 再来一次，确保彻底
        # 如果看起来像 JSON，再尝试解析
        if (cleaned.startswith('{') and cleaned.endswith('}')) or (
            cleaned.startswith('[') and cleaned.endswith(']')):
            try:
                parsed = json.loads(cleaned)
                return _recursive_clean(parsed)
            except json.JSONDecodeError:
                pass
        return cleaned
    # 其他类型原样返回
    return data

###########################################################################
#                           商品信息提取                                  #
###########################################################################

def extract_products_from_text(text: str):
    """按照新的规则直接解析字符串为 Python 对象（list/dict）。

    解析顺序:
        1. 尝试 json.loads
        2. 退回到 ast.literal_eval
        3. 失败则原样返回字符串
    """
    stripped = text.strip()
    try:
        return json.loads(stripped)
    except json.JSONDecodeError:
        try:
            return ast.literal_eval(stripped)
        except Exception:
            return stripped

###########################################################################
#                           业务核心逻辑                                   #
###########################################################################


def extract_biz_raw(doc: Dict[str, Any]) -> Optional[str]:
    """尝试在多种路径中寻找商品原始字符串或列表。"""
    # 1) hybrid_list -> sections -> 0 -> items
    try:
        items = doc["hybrid_list"]["sections"][0]["items"]
        return json.dumps(items, ensure_ascii=False)  # 转为字符串便于后续正则
    except (KeyError, IndexError, TypeError):
        pass

    # 2) tabkit_response -> tabs -> 1 -> biz_extra_info
    try:
        biz_extra = doc["tabkit_response"]["tabs"][1]["biz_extra_info"]
        # 该字段有时已是字符串，有时是对象
        if isinstance(biz_extra, (dict, list)):
            return json.dumps(biz_extra, ensure_ascii=False)
        return str(biz_extra)
    except (KeyError, IndexError, TypeError):
        pass

    # 找不到返回 None
    return None

###########################################################################
#                           主执行入口                                     #
###########################################################################

def main() -> None:
    # -------------------  解析命令行参数  ------------------- #
    parser = argparse.ArgumentParser(description="提取抖音商品信息并写入数据库并导出 JSON")
    parser.add_argument("--date", type=str, default=None,
                        help="指定读取 inserted_at 日期 (YYYY-MM-DD)。不提供则读取全部。")
    args = parser.parse_args()

    mongo = MongoManager()
    success, total = 0, 0
    results_output: List[Dict[str, Any]] = []  # 收集结果，最终写入 JSON

    try:
        for doc in mongo.fetch_documents(args.date):
            total += 1
            aweme_id = doc.get("aweme_id")
            keyword = doc.get("keyword")  # 读取关键词
            raw_text = extract_biz_raw(doc)
            if raw_text is None:
                continue  # 无可用数据
            cleaned_text = _recursive_clean(raw_text)
            if not isinstance(cleaned_text, str):
                cleaned_text = json.dumps(cleaned_text, ensure_ascii=False)

            data_parsed = extract_products_from_text(cleaned_text)
            if not data_parsed:
                continue
            now_date = datetime.date.today().isoformat()
            item_cnt = len(data_parsed) if isinstance(data_parsed, (list, dict)) else 1
            result_doc = {
                "aweme_id": aweme_id,
                "item_count": item_cnt,
                "data": data_parsed,
                "inserted_at": now_date,
                "keyword": keyword,
            }

            # 写入 MongoDB
            mongo.save_result(result_doc)

            # 收集结果以便最终写入 JSON 文件
            results_output.append(result_doc)
            success += 1

    finally:
        mongo.close()

    # -------------------  导出 JSON  ------------------- #
    if results_output:
        output_file = f"douyin_products_{datetime.date.today().isoformat()}.json"
        with open(output_file, "w", encoding="utf-8") as f:
            json.dump(results_output, f, ensure_ascii=False, indent=2, default=str)
        print(f"已导出数据至 {output_file}")

    print(f"处理完毕，总文档 {total} 条，成功写入 {success} 条，已保存至 JSON")


if __name__ == "__main__":
    main() 