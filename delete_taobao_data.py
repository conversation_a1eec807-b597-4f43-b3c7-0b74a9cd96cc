import pymongo
import json
from datetime import datetime


class TaobaoDataDeleter:
    def __init__(self):
        # 连接MongoDB数据库，使用与date.py相同的连接信息
        self.client = pymongo.MongoClient("**************************************************", compressors='zstd')
        # 创建淘宝抓包数据库和数据表
        self.db = self.client["taobao_capture_db"]
        self.collection = self.db["taobao_packet_data"]

    def delete_all_data(self):
        """删除所有数据"""
        try:
            result = self.collection.delete_many({})
            print(f"成功删除 {result.deleted_count} 条记录")
            return result.deleted_count
        except Exception as e:
            print(f"删除所有数据时出错: {e}")
            return 0

    def delete_by_date_range(self, start_date=None, end_date=None):
        """根据日期范围删除数据（如果数据中有时间字段）"""
        try:
            query = {}
            if start_date:
                query['timestamp'] = {'$gte': start_date}
            if end_date:
                if 'timestamp' in query:
                    query['timestamp']['$lte'] = end_date
                else:
                    query['timestamp'] = {'$lte': end_date}
            
            result = self.collection.delete_many(query)
            print(f"根据日期范围删除了 {result.deleted_count} 条记录")
            return result.deleted_count
        except Exception as e:
            print(f"根据日期范围删除数据时出错: {e}")
            return 0

    def delete_by_condition(self, condition):
        """根据自定义条件删除数据"""
        try:
            result = self.collection.delete_many(condition)
            print(f"根据条件删除了 {result.deleted_count} 条记录")
            return result.deleted_count
        except Exception as e:
            print(f"根据条件删除数据时出错: {e}")
            return 0

    def get_data_count(self):
        """获取数据总数"""
        try:
            count = self.collection.count_documents({})
            print(f"当前数据库中有 {count} 条记录")
            return count
        except Exception as e:
            print(f"获取数据总数时出错: {e}")
            return 0

    def backup_before_delete(self, backup_filename=None):
        """删除前备份数据到JSON文件"""
        if not backup_filename:
            backup_filename = f"taobao_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            data = list(self.collection.find({}))
            # 处理MongoDB的ObjectId，使其可以JSON序列化
            for item in data:
                if '_id' in item:
                    item['_id'] = str(item['_id'])
            
            with open(backup_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"数据已备份到: {backup_filename}")
            print(f"备份了 {len(data)} 条记录")
            return backup_filename
        except Exception as e:
            print(f"备份数据时出错: {e}")
            return None

    def close_connection(self):
        """关闭数据库连接"""
        self.client.close()
        print("数据库连接已关闭")


def main():
    deleter = TaobaoDataDeleter()
    
    print("=== 淘宝抓包数据删除工具 ===")
    print("1. 查看当前数据总数")
    print("2. 备份所有数据")
    print("3. 删除所有数据")
    print("4. 删除所有数据（带备份）")
    print("5. 退出")
    
    while True:
        try:
            choice = input("\n请选择操作 (1-5): ").strip()
            
            if choice == '1':
                deleter.get_data_count()
            
            elif choice == '2':
                backup_file = deleter.backup_before_delete()
                if backup_file:
                    print(f"备份完成: {backup_file}")
            
            elif choice == '3':
                confirm = input("确认要删除所有数据吗？这个操作不可逆！(输入 'YES' 确认): ")
                if confirm == 'YES':
                    deleted_count = deleter.delete_all_data()
                    print(f"删除操作完成，共删除 {deleted_count} 条记录")
                else:
                    print("操作已取消")
            
            elif choice == '4':
                confirm = input("确认要备份并删除所有数据吗？(输入 'YES' 确认): ")
                if confirm == 'YES':
                    # 先备份
                    backup_file = deleter.backup_before_delete()
                    if backup_file:
                        # 再删除
                        deleted_count = deleter.delete_all_data()
                        print(f"备份并删除完成，共删除 {deleted_count} 条记录")
                        print(f"备份文件: {backup_file}")
                    else:
                        print("备份失败，取消删除操作")
                else:
                    print("操作已取消")
            
            elif choice == '5':
                break
            
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n程序被用户中断")
            break
        except Exception as e:
            print(f"发生错误: {e}")
    
    deleter.close_connection()
    print("程序结束")


if __name__ == "__main__":
    main() 