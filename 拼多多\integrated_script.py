import argparse
import ast
import json
import sys
from pathlib import Path
from typing import Any, Dict, List

# 第三方依赖：PyYAML、pymongo
try:
    import yaml  # type: ignore
except ModuleNotFoundError:
    print("缺少 PyYAML 库，请执行: pip install pyyaml", file=sys.stderr)
    sys.exit(1)

try:
    import pymongo  # type: ignore
except ModuleNotFoundError:
    # 只有在使用 MongoDB 功能时才强制依赖
    pymongo = None  # type: ignore


###############################################################################
# 数据加载层
###############################################################################

# 默认 Mongo 参数（可按需修改）
DEFAULT_URI = "**************************************************"
DEFAULT_DB = "pinduoduo"
DEFAULT_COLLECTION = "shop_goods_list_20250726"

def _parse_goods_list(raw: Any) -> List[Dict[str, Any]]:
    """将多种格式的原始数据解析为商品列表。"""
    # 如果顶层是 dict，优先取 goods_list 字段
    if isinstance(raw, dict) and "goods_list" in raw:
        raw = raw["goods_list"]

    # 可能出现字符串化的列表，先尝试 json，再退回 ast
    if isinstance(raw, str):
        try:
            raw = json.loads(raw)
        except json.JSONDecodeError:
            raw = ast.literal_eval(raw)

    if not isinstance(raw, list):
        raise ValueError("无法识别的商品列表格式")

    return raw  # type: ignore[return-value]


def load_from_txt(txt_path: str) -> List[Dict[str, Any]]:
    """YAML/纯文本 -> dict -> goods_list"""
    content = Path(txt_path).read_text(encoding="utf-8", errors="ignore")
    data = yaml.safe_load(content)
    return _parse_goods_list(data)


def load_from_json(json_path: str) -> List[Dict[str, Any]]:
    data = json.loads(Path(json_path).read_text(encoding="utf-8", errors="ignore"))
    return _parse_goods_list(data)


def load_from_mongo(uri: str, db: str, collection: str, skip: int = 0) -> List[Dict[str, Any]]:
    if pymongo is None:
        raise ModuleNotFoundError("未安装 pymongo，请先执行: pip install pymongo")

    from pymongo.errors import ServerSelectionTimeoutError, ConnectionFailure

    client = pymongo.MongoClient(uri, serverSelectionTimeoutMS=10000)
    try:
        try:
            # 先做一次 ping 以快速检测连接可用性
            client.admin.command("ping")
        except (ServerSelectionTimeoutError, ConnectionFailure) as exc:
            raise ConnectionError(
                f"无法连接到 MongoDB：{exc}.\n请检查 --uri 是否正确、网络是否可达，以及用户名/密码是否正确。"
            ) from exc

        cursor = client[db][collection].find().skip(skip)  # 不设 limit，读取全部

        aggregated: List[Dict[str, Any]] = []
        count_docs = 0
        for doc in cursor:
            count_docs += 1
            try:
                goods_slice = _parse_goods_list(doc)
                if isinstance(goods_slice, list):
                    aggregated.extend(goods_slice)
                else:  # 如果解析后不是列表，视为单个商品条目
                    aggregated.append(goods_slice)  # type: ignore[arg-type]
            except Exception as exc:  # noqa: BLE001
                print(f"解析第 {count_docs} 条文档失败: {exc}", file=sys.stderr)

        if not aggregated:
            raise ValueError("集合读取完毕，但未解析到任何商品数据。")

        print(f"已从 MongoDB 读取 {count_docs} 条文档，合并得到 {len(aggregated)} 条商品记录。")
        return aggregated
    finally:
        client.close()


# --------------------------- 自动检测 --------------------------- #
def autodetect_goods() -> List[Dict[str, Any]]:
    """按固定优先级自动查找并加载数据。

    优先级：
        1. 数据.json
        2. 测试数据.json
        3. 数据.txt
        4. MongoDB 默认连接
    """

    search_plan = [
        ("数据.json", load_from_json),
        ("测试数据.json", load_from_json),
        ("数据.txt", load_from_txt),
    ]

    for path, loader in search_plan:
        p = Path(path)
        if p.exists():
            try:
                print(f"检测到本地文件: {path}，尝试读取 …")
                return loader(path)
            except Exception as exc:  # noqa: BLE001
                print(f"读取 {path} 失败: {exc}，继续下一种方式。", file=sys.stderr)

    print("未检测到本地数据文件，尝试连接 MongoDB 读取默认数据 …")
    return load_from_mongo(DEFAULT_URI, DEFAULT_DB, DEFAULT_COLLECTION, 0)


###############################################################################
# 解析层（来自 extract_products.py）
###############################################################################

def extract_products(goods_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
    """直接将每条记录序列化为标准 JSON 字符串返回"""
    return goods_list


###############################################################################
# JSON 写入层（一行一条数据）
###############################################################################

def write_to_json(products: List[Dict[str, Any]], json_path: str) -> None:
    """将商品数据写入JSON文件，每行一条记录"""
    with open(json_path, 'w', encoding='utf-8') as f:
        for item in products:
            # 每条记录序列化为JSON字符串并写入一行
            json.dump(item, f, ensure_ascii=False)
            f.write('\n')  # 换行分隔


###############################################################################
# CLI
###############################################################################

def main() -> None:
    parser = argparse.ArgumentParser(description="整合脚本：读取商品数据并导出到JSON文件（一行一条记录）")

    # 不再强制要求选择数据源，若均未指定则走自动检测流程
    src_group = parser.add_mutually_exclusive_group(required=False)
    src_group.add_argument("--txt", help="输入 TXT/YAML 文件路径")
    src_group.add_argument("--json", help="输入 JSON 文件路径")
    src_group.add_argument(
        "--mongo",
        action="store_true",
        help="从 MongoDB 读取（需同时提供 --uri --db --collection）",
    )

    parser.add_argument("--uri", default=DEFAULT_URI, help="MongoDB 连接 URI")
    parser.add_argument("--db", default=DEFAULT_DB, help="数据库名称")
    parser.add_argument("--collection", default=DEFAULT_COLLECTION, help="集合名称")
    parser.add_argument("--skip", type=int, default=0, help="跳过前 n 条记录（MongoDB）")

    parser.add_argument("--output", default="商品列表.json", help="输出 JSON 文件名")

    args = parser.parse_args()

    # 数据加载
    if args.txt:
        goods_raw = load_from_txt(args.txt)
    elif args.json:
        goods_raw = load_from_json(args.json)
    elif args.mongo:
        goods_raw = load_from_mongo(args.uri, args.db, args.collection, args.skip)
    else:
        goods_raw = autodetect_goods()

    # 数据解析
    products = extract_products(goods_raw)
    if not products:
        print("未解析到任何商品数据", file=sys.stderr)
        sys.exit(1)

    # 输出 JSON
    write_to_json(products, args.output)
    print(f"已成功写入 {len(products)} 条记录到 {args.output}（一行一条数据）")


if __name__ == "__main__":
    main()