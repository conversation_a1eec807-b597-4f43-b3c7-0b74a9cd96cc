import json
import sys
import os


def convert_to_json(input_path: str, output_path: str) -> None:
    """读取 input_path，将其内容按 YAML 解析为字典，再写入 output_path(JSON)。"""
    # 尝试导入 PyYAML，如果未安装则提醒用户
    try:
        import yaml  # type: ignore
    except ModuleNotFoundError:
        print(
            "检测到未安装 PyYAML 依赖，请先执行: pip install pyyaml",
            file=sys.stderr,
        )
        sys.exit(1)

    # 读取文件内容
    with open(input_path, "r", encoding="utf-8") as f:
        content = f.read()

    # 使用 YAML 解析（YAML 语法兼容性较强，适合处理“key: value”格式文件）
    try:
        data = yaml.safe_load(content)
    except Exception as exc:  # noqa: BLE001
        print("解析失败，错误信息：", exc, file=sys.stderr)
        sys.exit(1)

    # 将字典写入 JSON 文件
    with open(output_path, "w", encoding="utf-8") as f_out:
        json.dump(data, f_out, ensure_ascii=False, indent=2)

    print(f"已成功将 '{input_path}' 转换并保存至 '{output_path}'.")


if __name__ == "__main__":
    # 默认输入/输出路径
    default_input = "数据.txt"
    default_output = "数据.json"

    # 支持命令行参数: python 获取.py [输入文件] [输出文件]
    args = sys.argv[1:]
    input_file = args[0] if len(args) >= 1 else default_input
    output_file = args[1] if len(args) >= 2 else default_output

    if not os.path.exists(input_file):
        print(f"输入文件 '{input_file}' 不存在!", file=sys.stderr)
        sys.exit(1)

    convert_to_json(input_file, output_file)
