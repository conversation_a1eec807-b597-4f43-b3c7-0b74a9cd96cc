# #!/usr/bin/env python3
# # -*- coding: utf-8 -*-
# """
# mitmproxy 中间人抓包程序
# 功能：
# 1. 自动下载证书
# 2. 启动代理服务器
# 3. 拦截HTTP/HTTPS请求
# 4. 保存抓包数据
# """

# import os
# import sys
# import json
# import requests
# import threading
# import time
# import signal
# import atexit
# from datetime import datetime
# from mitmproxy import http, options, master
# from mitmproxy.tools.dump import DumpMaster
# import asyncio
# import base64
# import re
# from urllib.parse import unquote_plus


# class MitmProxyInterceptor:
#     def __init__(self, proxy_port=8080, save_dir="./captures", filter_prefixes=None):
#         """
#         初始化中间人抓包器
#         :param proxy_port: 代理端口，默认8080
#         :param save_dir: 数据保存目录
#         :param filter_prefixes: 允许用户指定仅拦截的 URL 前缀列表
#         """
#         self.proxy_port = proxy_port
#         self.save_dir = save_dir
#         # 允许用户指定仅拦截的 URL 前缀列表
#         self.filter_prefixes = filter_prefixes or []
#         self.captured_requests = []
#         self.cert_url = f"http://mitm.it/cert/pem"
        
#         # 创建保存目录
#         os.makedirs(save_dir, exist_ok=True)
        
#         # 生成时间戳用于文件命名
#         self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
#         print(f"[INFO] 初始化抓包器...")
#         print(f"[INFO] 代理端口: {self.proxy_port}")
#         print(f"[INFO] 数据保存目录: {self.save_dir}")
        
#         # 注册退出处理程序
#         signal.signal(signal.SIGINT, self.signal_handler)
#         signal.signal(signal.SIGTERM, self.signal_handler)

#     def signal_handler(self, signum, frame):
#         """
#         信号处理程序
#         """
#         print(f"\n[INFO] 接收到中断信号，退出程序...")
#         sys.exit(0)

#     def download_certificate(self):
#         """
#         下载 mitmproxy 证书到本地
#         """
#         try:
#             print(f"[INFO] 正在下载证书...")
            
#             # 下载证书
#             cert_response = requests.get(self.cert_url, timeout=10)
#             if cert_response.status_code == 200:
#                 cert_path = os.path.join(self.save_dir, "mitmproxy-ca-cert.pem")
#                 with open(cert_path, 'wb') as f:
#                     f.write(cert_response.content)
#                 print(f"[SUCCESS] 证书已下载到: {cert_path}")
#                 print(f"[INFO] 请手动安装此证书到系统信任根证书存储区")
#                 return True
#             else:
#                 print(f"[ERROR] 证书下载失败，状态码: {cert_response.status_code}")
#                 return False
                
#         except Exception as e:
#             print(f"[ERROR] 下载证书时出错: {str(e)}")
#             print(f"[INFO] 您也可以手动访问 http://mitm.it 下载证书")
#             return False

#     def request(self, flow: http.HTTPFlow) -> None:
#         """
#         拦截请求
#         """
#         # 若配置了过滤前缀且当前 URL 不匹配，则直接跳过
#         if self.filter_prefixes and not any(flow.request.pretty_url.startswith(p) for p in self.filter_prefixes):
#             return
        
#         # 原始请求体文本
#         raw_text = flow.request.get_text() if flow.request.get_text() else None

#         # 尝试对常见的 application/x-www-form-urlencoded 或包含 "data=" 参数的文本体进行 URL 解码
#         content_decoded = None
#         detail_algo_param = None
        
#         if raw_text and "%" in raw_text:
#             try:
#                 # 针对整体字符串做一次解码
#                 content_decoded_once = unquote_plus(raw_text)

#                 # 若仍含有 %xx 片段，则再解码一次（处理双重编码）
#                 if "%" in content_decoded_once and re.search(r"%[0-9a-fA-F]{2}", content_decoded_once):
#                     content_decoded_once = unquote_plus(content_decoded_once)

#                 # 解析并提取 detailAlgoParam 值，返回简化字典
#                 def extract_algo_param_only(decoded_str: str):
#                     # 移除前缀 'data='（如果存在）
#                     if decoded_str.startswith('data='):
#                         decoded_str_wo_prefix = decoded_str[5:]
#                     else:
#                         decoded_str_wo_prefix = decoded_str

#                     try:
#                         root_obj = json.loads(decoded_str_wo_prefix)
#                     except Exception:
#                         return {"detailAlgoParam": None}  # 解析失败返回空值

#                     item_params_raw = root_obj.get('itemParams')
#                     algo_value = None
#                     if item_params_raw:
#                         try:
#                             item_params_obj = json.loads(item_params_raw)
#                             for v in item_params_obj.values():
#                                 try:
#                                     item_obj = json.loads(v)
#                                     if 'detailAlgoParam' in item_obj:
#                                         algo_value = item_obj['detailAlgoParam']
#                                         break
#                                 except Exception:
#                                     continue
#                         except Exception:
#                             pass

#                     # 返回简化的字典格式
#                     return {"detailAlgoParam": algo_value}

#                 content_decoded_processed = extract_algo_param_only(content_decoded_once)
#                 content_decoded = content_decoded_processed
                
#                 # 提取detailAlgoParam的值，用于后续插入到响应中
#                 if content_decoded and content_decoded.get('detailAlgoParam'):
#                     detail_algo_param = content_decoded['detailAlgoParam']
                    
#             except Exception:
#                 pass

#         # 将detailAlgoParam存储到flow的metadata中，供响应处理时使用
#         if detail_algo_param:
#             if not hasattr(flow, 'metadata'):
#                 flow.metadata = {}
#             flow.metadata['detailAlgoParam'] = detail_algo_param

#         # 不再保存空的请求数据，只保存包含detailAlgoParam的响应数据
#         # request_data = {
#         #     # "timestamp": datetime.now().isoformat(),
#         #     # "type": "request",
#         #     # "method": flow.request.method,
#         #     # "url": flow.request.pretty_url,
#         #     # "content": raw_text,
#         #     # "content_decoded": content_decoded,
#         #     # "host": flow.request.pretty_host,
#         #     # "path": flow.request.path,
#         #     # "scheme": flow.request.scheme,
#         #     # "port": flow.request.port,
#         #     # # 解码后的 URL，便于阅读及后续分析
#         #     # "url_decoded": unquote_plus(flow.request.pretty_url)
#         # }
        
#         # self.captured_requests.append(request_data)
#         print(f"[REQUEST] {flow.request.method} {flow.request.pretty_url}")
#         if detail_algo_param:
#             print(f"[INFO] 提取到 detailAlgoParam: {detail_algo_param}")
        
#         # 不需要实时保存空数据
#         # self.save_data()

#     def response(self, flow: http.HTTPFlow) -> None:
#         """
#         拦截响应
#         """
#         # 同样应用过滤逻辑
#         if self.filter_prefixes and not any(flow.request.pretty_url.startswith(p) for p in self.filter_prefixes):
#             return
        
#         # 默认值
#         content_text = None
#         content_raw_base64 = None

#         # 优先尝试获取文本内容
#         try:
#             content_text = flow.response.get_text()

#             # 如果文本中包含 unicode 转义字符 (如 "\u4e2d\u6587"), 尝试反转义成可读文字
#             if content_text and "\\u" in content_text:
#                 try:
#                     # 使用正则快速判断是否为连续的 unicode 转义序列
#                     if re.search(r"\\u[0-9a-fA-F]{4}", content_text):
#                         content_text = bytes(content_text, "utf-8").decode("unicode_escape")
#                 except Exception:
#                     # 解码失败则保持原样
#                     pass

#             # 如果内容包含典型的百分号 URL 编码(如 "%E4%B8%AD"), 进一步尝试解码
#             if content_text and "%" in content_text and re.search(r"%[0-9a-fA-F]{2}", content_text):
#                 try:
#                     decoded_once = unquote_plus(content_text)
#                     # 若仍然存在编码片段，再解码一次(处理多重编码)
#                     if decoded_once != content_text and re.search(r"%[0-9a-fA-F]{2}", decoded_once):
#                         decoded_once = unquote_plus(decoded_once)
#                     content_text = decoded_once
#                 except Exception:
#                     pass
#         except Exception:
#             # 获取文本失败，继续处理
#             pass

#         # 尝试将detailAlgoParam插入到content_text的JSON中
#         if content_text and hasattr(flow, 'metadata') and flow.metadata.get('detailAlgoParam'):
#             try:
#                 # 尝试解析JSON
#                 json_data = json.loads(content_text)
                
#                 # 插入detailAlgoParam到JSON根级别
#                 json_data['detailAlgoParam'] = flow.metadata['detailAlgoParam']
                
#                 # 将修改后的JSON转换回字符串
#                 content_text = json.dumps(json_data, ensure_ascii=False, separators=(',', ':'))
                
#                 print(f"[INFO] 已将 detailAlgoParam ({flow.metadata['detailAlgoParam']}) 插入到响应JSON中")
                
#             except Exception as e:
#                 # JSON解析或插入失败，保持原内容不变
#                 print(f"[WARNING] 无法将detailAlgoParam插入到响应中: {str(e)}")
#                 pass

#         # 无论是否获取到文本，都保存完整原始数据（base64），方便后续分析
#         try:
#             if flow.response.content:
#                 content_raw_base64 = base64.b64encode(flow.response.content).decode("ascii")
#         except Exception:
#             pass

#         # 直接保存content_text的值，不包装在对象中
#         if content_text:
#             self.captured_requests.append(content_text)
        
#         print(f"[RESPONSE] {flow.response.status_code} {flow.request.pretty_url}")
#         if content_text:
#             print(f"[INFO] 保存了响应数据，长度: {len(content_text)} 字符")
        
#         # 实时保存数据
#         self.save_data()

#     def save_data(self):
#         """
#         保存抓包数据到JSON文件
#         """
#         try:
#             filename = f"capture_{self.timestamp}.json"
#             filepath = os.path.join(self.save_dir, filename)
            
#             # 创建保存目录
#             os.makedirs(self.save_dir, exist_ok=True)
            
#             with open(filepath, 'w', encoding='utf-8') as f:
#                 json.dump(self.captured_requests, f, ensure_ascii=False, indent=2)
                
#             # 每100个请求打印一次保存信息
#             if len(self.captured_requests) % 100 == 0:
#                 print(f"[INFO] 已保存 {len(self.captured_requests)} 条数据到 {filepath}")
                
#         except Exception as e:
#             print(f"[ERROR] 保存数据时出错: {str(e)}")

#     def start_proxy(self):
#         """
#         启动代理服务器
#         """
#         try:
#             print(f"[INFO] 启动代理服务器...")
            
#             # 配置选项
#             opts = options.Options(
#                 listen_port=self.proxy_port,
#                 listen_host="127.0.0.1"
#             )
            
#             # 创建master
#             master_instance = DumpMaster(opts)
            
#             # 添加插件
#             master_instance.addons.add(self)
            
#             print(f"[SUCCESS] 代理服务器已启动!")
#             print(f"[INFO] 代理地址: 127.0.0.1:{self.proxy_port}")
#             print(f"[INFO] 请在浏览器或应用中设置HTTP代理为上述地址")
#             print(f"[INFO] 按 Ctrl+C 停止抓包")
            
#             # 修复Windows事件循环问题
#             try:
#                 # 先尝试获取现有的事件循环
#                 loop = asyncio.get_event_loop()
#                 if loop.is_running():
#                     # 如果已有运行中的循环，创建新任务
#                     task = loop.create_task(master_instance.run())
#                     loop.run_until_complete(task)
#                 else:
#                     # 如果没有运行中的循环，直接运行
#                     loop.run_until_complete(master_instance.run())
#             except RuntimeError:
#                 # 如果没有事件循环，创建新的
#                 asyncio.run(master_instance.run())
            
#         except KeyboardInterrupt:
#             print(f"\n[INFO] 用户中断，正在停止抓包...")
#         except Exception as e:
#             print(f"[ERROR] 启动代理服务器时出错: {str(e)}")
#             print(f"[DEBUG] 错误详情: {type(e).__name__}: {str(e)}")
#             # 提供备用解决方案
#             print(f"[INFO] 尝试备用启动方式...")
#             self.start_proxy_alternative()

#     def start_proxy_alternative(self):
#         """
#         备用启动方式 - 使用线程运行
#         """
#         try:
#             print(f"[INFO] 使用备用方式启动代理服务器...")
            
#             # 配置选项
#             opts = options.Options(
#                 listen_port=self.proxy_port,
#                 listen_host="127.0.0.1"
#             )
            
#             # 创建master
#             master_instance = DumpMaster(opts)
            
#             # 添加插件
#             master_instance.addons.add(self)
            
#             # 使用新的事件循环
#             def run_in_thread():
#                 new_loop = asyncio.new_event_loop()
#                 asyncio.set_event_loop(new_loop)
#                 new_loop.run_until_complete(master_instance.run())
#                 new_loop.close()
            
#             # 在新线程中运行
#             proxy_thread = threading.Thread(target=run_in_thread, daemon=True)
#             proxy_thread.start()
            
#             print(f"[SUCCESS] 备用代理服务器已启动!")
#             print(f"[INFO] 代理地址: 127.0.0.1:{self.proxy_port}")
            
#             # 主线程等待用户中断
#             try:
#                 while proxy_thread.is_alive():
#                     time.sleep(1)
#             except KeyboardInterrupt:
#                 print(f"\n[INFO] 用户中断，正在停止抓包...")
#         except Exception as e:
#             print(f"[ERROR] 备用启动方式也失败: {str(e)}")
#             print(f"[INFO] 请尝试在命令行中直接运行: mitmdump -s test.py -p {self.proxy_port}")


# def main():
#     """
#     主函数
#     """
#     print("="*60)
#     print("            mitmproxy 中间人抓包工具")
#     print("="*60)
    
#     # 检查mitmproxy是否已安装
#     try:
#         import mitmproxy
#         print(f"[INFO] mitmproxy 版本: {mitmproxy.version.VERSION}")
#     except ImportError:
#         print("[ERROR] 未安装 mitmproxy，请先运行: pip install mitmproxy")
#         return
    
#     # 仅拦截指定 URL 前缀，可按需调整或通过参数传入
#     target_prefixes = [
#         "https://trade-acs.m.taobao.com/gw/mtop.taobao.detail.batchgetdetail/1.0/"
#     ]

#     # 创建抓包器实例
#     interceptor = MitmProxyInterceptor(proxy_port=8080, filter_prefixes=target_prefixes)
    
#     # 下载证书
#     cert_downloaded = interceptor.download_certificate()
#     if not cert_downloaded:
#         print("[WARNING] 证书下载失败，但仍可继续抓包HTTP流量")
    
#     print("\n" + "="*60)
#     print("请完成以下步骤:")
#     print("1. 安装下载的证书 (mitmproxy-ca-cert.pem)")
#     print("2. 在浏览器/应用中设置HTTP代理为 127.0.0.1:8080")
#     print("3. 开始浏览网页进行抓包")
#     print("="*60)
#     print("\n💡 如果程序启动失败，您也可以使用命令行方式:")
#     print(f"   mitmdump -s {__file__} -p 8080")
#     print("="*60 + "\n")
    
#     input("按 Enter 键启动代理服务器...")
    
#     # 启动代理服务器
#     interceptor.start_proxy()


# # 为 mitmdump/mitmproxy 脚本模式提供插件列表
# # mitmproxy 在加载脚本时会查找名为 `addons` 的可迭代对象，而不是函数。
# # 之前这里定义为函数导致返回值不可迭代，被视为普通函数对象，引发
# # "'function' object is not iterable" 异常。

# # 创建并暴露全局插件实例列表供 mitmproxy 识别。
# # 如需自定义端口，请在此处调整或通过环境变量等方式传入。

# global_interceptor = MitmProxyInterceptor(filter_prefixes=[
#     "https://trade-acs.m.taobao.com/gw/mtop.taobao.detail.batchgetdetail/1.0/"
# ])

# # mitmproxy 会直接遍历该列表中的插件实例
# addons = [global_interceptor]


# if __name__ == "__main__":
#     main()
 # 将url和对应的响应写入文本文件
        url = flow.request.url
        print('链接:',url)
        try:
            response_json = flow.response.json()
        except Exception as e:
            response_json = {"error": f"解析响应失败: {e}"}
        with open("response_log.txt", "a", encoding="utf-8") as f:
            f.write("URL: " + url + "\n")
            f.write("响应结果: " + str(response_json) + "\n")
            f.write("="*80 + "\n")

            # https://ecom5-normal-m.ecombdapi.com/shop/bff/tab/?sec_shop_id=IoNkmaP&hit_bottom_tab_test=1&size=10&activate_pick_type=0&activate_tab_type=3&custom_id_type=4&custom_ids=3751166952576516370%2C3741129706389504326%2C3735409325347307947%2C3731157359238709427%2C3733927392046088333%2C3740067835813167585%2C3730571433508733186%2C3742090471254655341%2C3762273040256335984%2C3753412666656424817%2C3752235362391556385%2C3747102953731915783%2C3740217302965158209%2C3750621090351808662%2C3750622181139284421%2C3742091867085472053%2C3745370505507635618%2C3752478068728529068%2C3730574278924566960%2C3755027402527474006%2C3762678631038583051%2C3730562242203222461%2C3730568111250866493%2C3730572464233775223%2C3750633796886724647%2C3736475946132308220%2C3736475909784469850%2C3730580551724302466%2C3730576729027248535%2C3739109770368516539%2C3742040050477695332%2C3682561110030024804%2C3705724995985604890%2C3742088869357683010%2C3742098743386833041%2C3729616971390648559%2C3746489295297446002%2C3730563781848334399%2C3737578722010726494%2C3750950144976224600%2C3745408644901110214%2C3745409244241986281%2C3736710640484614152%2C3734998473188180439%2C3752271985544134985%2C3730575771476033974%2C3730561638659653931%2C3620506866943156740%2C3731156526031831059%2C3757856873261432848%2C3730577575169360140%2C3730561832050622726%2C3736162065618960730%2C3731156120300028147%2C3731156382318198949%2C3755020021450015180%2C3750633652954989027%2C3730563902182916388%2C3730559963748237807%2C3740217399484481975%2C3736661978446430681%2C3729616610688892993%2C3730561166280360357%2C3730566238796120512%2C3730564089013993563%2C3742812309219835924%2C3740046060320587831%2C3729617755213791534%2C3730564877266321611%2C3740220929997930897%2C3740045523634225724%2C3740037021637411288%2C3740217343809290240%2C3730567872930513048%2C3731337153045725361%2C3736885793327808943%2C3736887111907934729%2C3730931132716417357%2C3745404526128136389%2C3752271352070013209%2C3669364883142606876%2C3741130952047460760%2C3735225082080002069%2C3746490085370102259%2C3625754151037892507%2C3625749914002317022%2C3735410678421390283%2C3745759292389720314%2C3744463322716700816%2C3730565087619056115%2C3730568261583110185%2C3723489914877248130%2C3730571336737751353%2C3730575129302925459%2C3731339444477886558%2C3736165005498908796%2C3730572625387323706%2C3730556523471044865%2C3744300145995547098%2C3744301415217103241%2C3730577888743915561%2C3730577422882570418%2C3756387064493506579%2C3755779977275113959%2C3729614836892565897%2C3693601916153102575%2C3730567593807970509%2C3730568074861084883%2C3709087631217393702%2C3600163215897676323%2C3700707319022223509%2C3730565955311501723%2C3560408731428752907%2C3730351363696230795%2C3729665839704703279%2C3750452879920595097%2C3750453539323904565%2C3750452027621245283%2C3750452867044081926%2C3750437416092172340%2C3750452175764062495%2C3741723580166176928%2C3741725508723933453%2C3740220249346277839%2C3743900239501853064%2C3730569874284609861%2C3664180098917121073%2C3730568942352203785%2C3654337098472923291%2C3553027523040394007%2C3735225556766163279%2C3730568989638787088%2C3745251118503034966%2C3729615835447296358%2C3730564694604382449%2C3705328697239273908%2C3479545633457742549%2C3744273250918727847%2C3760811091203064103%2C3760811014053036325&entrance_location=search_order_center_shop_card&pre_store_group_type=open_url&pre_store_source_page=search_order_center&pass_through_api=%7B%22product_doc_info%22%3A%22%5B%7B%5C%22ctr%5C%22%3A0.03554439917206764%2C%5C%22cvr%5C%22%3A0.007039045449346304%2C%5C%22pid%5C%22%3A3751166952576516370%2C%5C%22rel%5C%22%3A0.921468198299408%7D%2C%7B%5C%22ctr%5C%22%3A0.03096751682460308%2C%5C%22cvr%5C%22%3A0.0061212810687720776%2C%5C%22pid%5C%22%3A3741129706389504326%2C%5C%22rel%5C%22%3A0.9265629649162292%7D%2C%7B%5C%22ctr%5C%22%3A0.020645929500460625%2C%5C%22cvr%5C%22%3A0.0029008574783802032%2C%5C%22pid%5C%22%3A3735409325347307947%2C%5C%22rel%5C%22%3A0.8501203656196594%7D%2C%7B%5C%22ctr%5C%22%3A0.031143832951784134%2C%5C%22cvr%5C%22%3A0.008412977680563927%2C%5C%22pid%5C%22%3A3731157359238709427%2C%5C%22rel%5C%22%3A0.9341383576393127%7D%2C%7B%5C%22ctr%5C%22%3A0.014281935058534145%2C%5C%22cvr%5C%22%3A0.004792316351085901%2C%5C%22pid%5C%22%3A3754667164167176221%2C%5C%22rel%5C%22%3A0.9214364290237427%7D%2C%7B%5C%22ctr%5C%22%3A0.021491764113307%2C%5C%22cvr%5C%22%3A0.003260345896705985%2C%5C%22pid%5C%22%3A3751166360030413612%2C%5C%22rel%5C%22%3A0.8967152833938599%7D%2C%7B%5C%22ctr%5C%22%3A0.01353214867413044%2C%5C%22cvr%5C%22%3A0.004348381422460079%2C%5C%22pid%5C%22%3A3733927392046088333%2C%5C%22rel%5C%22%3A0.791279673576355%7D%2C%7B%5C%22ctr%5C%22%3A0.012821214273571968%2C%5C%22cvr%5C%22%3A0.005554925184696913%2C%5C%22pid%5C%22%3A3717180185049694257%2C%5C%22rel%5C%22%3A0.9032370448112488%7D%2C%7B%5C%22ctr%5C%22%3A0.018833130598068237%2C%5C%22cvr%5C%22%3A0.002463006880134344%2C%5C%22pid%5C%22%3A3740067835813167585%2C%5C%22rel%5C%22%3A0.7801607847213745%7D%2C%7B%5C%22ctr%5C%22%3A0.016277460381388664%2C%5C%22cvr%5C%22%3A0.004591683857142925%2C%5C%22pid%5C%22%3A3730571433508733186%2C%5C%22rel%5C%22%3A0.8947589993476868%7D%2C%7B%5C%22ctr%5C%22%3A0.01805533468723297%2C%5C%22cvr%5C%22%3A0.0037217086646705866%2C%5C%22pid%5C%22%3A3742090471254655341%2C%5C%22rel%5C%22%3A0.9219312071800232%7D%2C%7B%5C%22ctr%5C%22%3A0.01590639166533947%2C%5C%22cvr%5C%22%3A0.003273064736276865%2C%5C%22pid%5C%22%3A3738155207469695016%2C%5C%22rel%5C%22%3A0.7606335282325745%7D%2C%7B%5C%22ctr%5C%22%3A0.02400774508714676%2C%5C%22cvr%5C%22%3A0.004520817659795284%2C%5C%22pid%5C%22%3A3741130952047460760%2C%5C%22rel%5C%22%3A0.8833214640617371%7D%2C%7B%5C%22ctr%5C%22%3A0.02400774508714676%2C%5C%22cvr%5C%22%3A0.006217078305780888%2C%5C%22pid%5C%22%3A3752475165322248514%2C%5C%22rel%5C%22%3A0.9239177703857422%7D%2C%7B%5C%22ctr%5C%22%3A0.018546566367149353%2C%5C%22cvr%5C%22%3A0.0024343819823116064%2C%5C%22pid%5C%22%3A3678061015121133776%2C%5C%22rel%5C%22%3A0.7999712228775024%7D%2C%7B%5C%22ctr%5C%22%3A0.01901443488895893%2C%5C%22cvr%5C%22%3A0.003389783203601837%2C%5C%22pid%5C%22%3A3730572464233775223%2C%5C%22rel%5C%22%3A0.9245626926422119%7D%2C%7B%5C%22ctr%5C%22%3A0.01750955730676651%2C%5C%22cvr%5C%22%3A0.003087369492277503%2C%5C%22pid%5C%22%3A3742091867085472053%2C%5C%22rel%5C%22%3A0.9016019105911255%7D%2C%7B%5C%22ctr%5C%22%3A0.013325147330760956%2C%5C%22cvr%5C%22%3A0.004485794343054295%2C%5C%22pid%5C%22%3A3731337153045725361%2C%5C%22rel%5C%22%3A0.9009609818458557%7D%2C%7B%5C%22ctr%5C%22%3A0.02517884224653244%2C%5C%22cvr%5C%22%3A0.005220125429332256%2C%5C%22pid%5C%22%3A3755760007916945453%2C%5C%22rel%5C%22%3A0.9243983626365662%7D%2C%7B%5C%22ctr%5C%22%3A0.015072628855705261%2C%5C%22cvr%5C%22%3A0.0024343819823116064%2C%5C%22pid%5C%22%3A3737448378301284453%2C%5C%22rel%5C%22%3A0.8786779046058655%7D%5D%22%2C%22ecom_scene_id%22%3A%221099%2C1031%2C1086%2C1003%22%2C%22brand_ids%22%3A%221166102886%22%2C%22query_info%22%3A%22%7B%5C%22search_hint%5C%22%3A%5C%22%E6%B4%97%E8%A1%A3%E6%9C%BA%5C%22%2C%5C%22forecast_cate%5C%22%3A%5C%2220044%5C%22%2C%5C%22origin_query%5C%22%3A%5C%22%E6%B5%B7%E5%B0%94%E6%B4%97%E8%A1%A3%E6%9C%BA%E6%97%97%E8%88%B0%E5%BA%97%E5%92%8C%E7%BE%8E%E7%9A%84%E5%AE%98%E6%96%B9%E6%97%97%E8%88%B0%E5%BA%97%5C%22%7D%22%2C%22entrance_location%22%3A%22search_order_center_shop_card%22%2C%22pre_store_source_page%22%3A%22search_order_center%22%2C%22pre_store_group_type%22%3A%22%22%2C%22pre_group_id%22%3A%22%22%2C%22pre_product_id%22%3A%22%22%2C%22pre_live_id%22%3A%22%22%2C%22store_type%22%3A%22shop%22%2C%22live_user_act_params%22%3A%22%22%2C%22sql_data_from_client%22%3A%22%7B%7D%22%2C%22is_first_enter%22%3A1%7D&search_params=%7B%22search_id%22%3A%22202507231656585CB6700D91C0B859A916%22%2C%22search_result_id%22%3A%221410370%22%2C%22search_jump%22%3A1%2C%22previous_page%22%3A%22search_order_center%22%7D&hit_static_ui_change=1&preload_request=0&client_experiment_list=%7B%22ec_shop_big_screen_adapt%22%3A0%2C%22mall_multi_tab%22%3Afalse%2C%22shop_info_style_by_shop_type%22%3A0%2C%22shop_tab_style_by_shop_type%22%3A0%2C%22ec_shop_interaction_opt%22%3A0%2C%22shop_product_layout_opt%22%3A0%2C%22ec_shop_live_tab%22%3A0%2C%22ec_shop_product_card_style_opt%22%3A0%2C%22ec_shop_product_list_params_opt%22%3A1%2C%22ec_shop_recommend_priority%22%3A0%2C%22ec_shop_recommend_style%22%3A1%2C%22shop_product_sell_point_type%22%3A2%2C%22ec_shop_switch_top_search_info%22%3A1%2C%22shop_celebrity_kol%22%3A0%2C%22shop_product_list_style%22%3A1%2C%22ec_shop_live_header_downgrade%22%3A0%2C%22ec_shop_type_perf_opt%22%3A1%2C%22shop_product_card_xml_preload%22%3A1%2C%22ec_shop_show_slide_hint%22%3A1%2C%22shop_hybrid_list_pre_bind_count%22%3A0%7D&local_decoration_channels=%7B%22ecommerce_shop_build_aweme%22%3A-1%2C%22ecommerce_shop_dynamic_components_aweme%22%3A-1%7D&iid=2042239936450682&device_id=2042239936446586&ac=wifi&channel=huawei_1128_64&aid=1128&app_name=aweme&version_code=310700&version_name=31.7.0&device_platform=android&os=android&ssmix=a&device_type=Redmi+Note+8&device_brand=xiaomi&language=zh&os_api=30&os_version=11&manifest_version_code=310701&resolution=1080*2216&dpi=440&update_version_code=31709900&_rticket=1753262126414&first_launch_timestamp=1753259825&last_deeplink_update_version_code=0&cpu_support64=true&host_abi=arm64-v8a&is_guest_mode=0&app_type=normal&minor_status=0&appTheme=light&is_preinstall=0&need_personal_recommend=1&is_android_pad=0&is_android_fold=0&ts=1753262126&cdid=521fb69d-082c-4276-852d-0283ba08cd42
# https://ecom5-normal-m.ecombdapi.com/aweme/v1/store/product/bff/

# self.redis = redis.StrictRedis(host='**************', port=6379, db=2, decode_responses=True)

        # pass
        # print('1')
        # if 'campaign.m.taobao.com/async/execute' in flow.request.url:


        # mitmdump -q -p 18080 --mode upstream:http://**************:8888 -s .\proxy_tb.py
# mitmdump -q -p 18080 --mode upstream:http://**************:9104 -s .\proxy_tb.py
# mitmdump -q -p 8080 -s .\date.py