{"api": "mtop.taobao.detail.batchgetdetail", "detailAlgoParam": "手机", "data": {"833003275432": {"data": {"apiStack": [{"data": {"container": {"data": [{"name": "detail_v3_appraise", "containerType": "dinamicx", "version": "80", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_appraise/1747624030479/detail_v3_appraise.zip", "md5": null, "type": ["dinamicx$detail_v3_appraise$0$80"]}, {"name": "detail_v3_headersku", "containerType": "dinamicx", "version": "128", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_headersku/1737527075025/detail_v3_headersku.zip", "md5": null, "type": ["dinamicx$detail_v3_headersku$0$128"]}, {"name": "detail_v3_inclusive_bottom_bar", "containerType": "dinamicx", "version": "9", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_inclusive_bottom_bar/1748919314991/detail_v3_inclusive_bottom_bar.zip", "md5": null, "type": ["dinamicx$detail_v3_inclusive_bottom_bar$0$9"]}, {"name": "detail_v3_part_division", "containerType": "dinamicx", "version": "12", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_part_division/1698657679576/detail_v3_part_division.zip", "md5": null, "type": ["dinamicx$detail_v3_part_division$0$12"]}, {"name": "detail_v3_price", "containerType": "dinamicx", "version": "85", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_price/1748395727273/detail_v3_price.zip", "md5": null, "type": ["dinamicx$detail_v3_price$0$85"]}, {"name": "detail_v3_shop_card", "containerType": "dinamicx", "version": "17", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_shop_card/1694587183544/detail_v3_shop_card.zip", "md5": null, "type": ["dinamicx$detail_v3_shop_card$0$17"]}, {"name": "detail_v3_title", "containerType": "dinamicx", "version": "71", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_title/1717405081403/detail_v3_title.zip", "md5": null, "type": ["dinamicx$detail_v3_title$0$71"]}, {"name": "linear", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$linear$0$0"]}, {"name": "locator", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$locator$0$0"]}, {"name": "overlay", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$overlay$0$0"]}, {"name": "sticky", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$sticky$0$0"]}]}, "data": {"headerPic": {"tag": "headerPic", "type": "native$headerpic", "fields": {"dimension": "3:4", "independentAURANode": "true", "paddingBottom": "0"}}, "mainContainer": {"tag": "mainContainer", "type": "layout$linear$0$0", "fields": {"direction": "horizontal", "pullStyle": {"position": "right", "pullText": "滑\n动\n发\n现\n更\n多\n内\n容", "releaseText": "释\n放\n发\n现\n更\n多\n内\n容"}, "scrollMode": "page"}, "events": {"dragEvent": [{"type": "locatorTo", "fields": {"locatorId": "desc"}}, {"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-ItemDetailRecommend", "args": {}, "eventId": "2201", "page": "Page_Detail"}}]}}, "frameGroup_video": {"tag": "frameGroup", "id": "video", "type": "layout$locator$0$0", "fields": {"locatorComponent": ["locatorBar", "progressBar"], "locatorId": "video"}}, "frame_video_0": {"tag": "frame", "id": "video_0", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false", "tag": "宝贝讲解"}}, "frameVideo_video_0": {"tag": "frameVideo", "id": "video_0", "type": "native$headervideo", "fields": {"code": "video_0", "contentStyle": {"paddingBottom": "0"}, "subType": "override", "thumbnailDimension": "1:1", "thumbnailUrl": "http://img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg", "type": "video", "videoId": "518219801607", "videoUrl": "http://cloud.video.taobao.com/play/u/**********/p/2/e/6/t/1/518219801607.mp4?appKey=38829"}}, "frameGroup_image": {"tag": "frameGroup", "id": "image", "type": "layout$locator$0$0", "fields": {"locatorComponent": ["locatorBar", "progressBar"], "locatorId": "image"}}, "frame_pic_0": {"tag": "frame", "id": "pic_0", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_0": {"tag": "frameImage", "id": "pic_0", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_0", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg"}}, "frame_pic_1": {"tag": "frame", "id": "pic_1", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_1": {"tag": "frameImage", "id": "pic_1", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_1", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i3/**********/O1CN01KvB3ck22AElxdt3rS_!!**********.jpg"}}, "frame_pic_2": {"tag": "frame", "id": "pic_2", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_2": {"tag": "frameImage", "id": "pic_2", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_2", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/**********/O1CN012bCS8Y22AEjrsfugv_!!**********.jpg"}}, "frame_pic_3": {"tag": "frame", "id": "pic_3", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_3": {"tag": "frameImage", "id": "pic_3", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_3", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/**********/O1CN01SvXUvx22AEjpiAWf3_!!**********.jpg"}}, "frame_pic_4": {"tag": "frame", "id": "pic_4", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_4": {"tag": "frameImage", "id": "pic_4", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_4", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/**********/O1CN01sGqrUc22AEoOjFxXd_!!**********.png"}}, "frame_pic_5": {"tag": "frame", "id": "pic_5", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_5": {"tag": "frameImage", "id": "pic_5", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_5", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/**********/O1CN01Kxymgn22AEoKdUEfw_!!**********.png"}}, "frame_pic_6": {"tag": "frame", "id": "pic_6", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_6": {"tag": "frameImage", "id": "pic_6", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_6", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/**********/O1CN01qj6Zdh22AEjrFCiyb_!!**********.jpg"}}, "frame_pic_7": {"tag": "frame", "id": "pic_7", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_7": {"tag": "frameImage", "id": "pic_7", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_7", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/**********/O1CN01ZorvPj22AEjrrgP0n_!!**********.jpg"}}, "frame_pic_8": {"tag": "frame", "id": "pic_8", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_8": {"tag": "frameImage", "id": "pic_8", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_8", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/**********/O1CN017Bc4x022AEjvq7PMA_!!**********.jpg"}}, "frame_pic_9": {"tag": "frame", "id": "pic_9", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_9": {"tag": "frameImage", "id": "pic_9", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_9", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/**********/O1CN01ODOrSV22AEjv5x5DP_!!**********.png"}}, "progressArea": {"tag": "progressArea", "type": "layout$sticky$0$0", "fields": {"marginBottom": "0", "marginTop": "-0", "position": "floatBottom", "showType": "showAlways"}}, "progressBar": {"tag": "progressBar", "type": "native$progressbar", "fields": {"contentStyle": {"paddingBottom": "0"}, "frameTag": {}}}, "locatorArea": {"tag": "locatorArea", "type": "layout$sticky$0$0", "fields": {"marginTop": "0", "position": "floatTop", "showType": "showAlways"}}, "locatorBar": {"tag": "locatorBar", "type": "native$locatorbar", "fields": {"hideLocator": "false", "isAURALeafNode": "true", "style": {"backgroundColor": "#4D242424", "initShowMaxCount": "3", "selectedAnchorBackgroundColor": "#ffffff", "selectedTextColor": "#000000", "unselectedTextColor": "#FFFFFF"}}, "events": {"exposureItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Show_DetailLocator_industry", "args": {"item_id": "833003275432", "seller_id": "**********", "shop_id": "107922698", "spm": "a2141.7631564.detaillocator"}, "eventId": "2201", "page": "Page_Detail"}}]}}, "locatorItem_video": {"tag": "locatorItem", "id": "video", "type": "native$locatoritem", "fields": {"fixed": "false", "locator": "video", "text": "视频"}, "events": {"itemClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_DetailLocator_industry", "args": {"bizCode": "video", "item_id": "833003275432", "seller_id": "**********", "shop_id": "107922698"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "locatorItem_image": {"tag": "locatorItem", "id": "image", "type": "native$locatoritem", "fields": {"fixed": "false", "locator": "image", "text": "图集"}, "events": {"itemClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_DetailLocator_industry", "args": {"bizCode": "image", "item_id": "833003275432", "seller_id": "**********", "shop_id": "107922698"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "detail3TopLine": {"tag": "detail3TopLine", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#FFFFFF", "height": "10"}}, "detailHeaderSKU": {"tag": "detailHeaderSKU", "type": "dinamicx$detail_v3_headersku$0$128", "fields": {"_dHeight": "52", "hiddenArrow": "false", "mainImage": {"isSelected": "true", "url": "http://img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg"}, "middleText": "款式选择", "selectedIndex": "\"1\"", "showBottomSKU": "false", "skuContents": [{"img": "https://img.alicdn.com/imgextra/i1/6000000000150/O1CN01Pv30fD1CykOnH25k1_!!6000000000150-2-remus.png", "isHot": "false", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i3/6000000000672/O1CN014pVV4U1Gpp94iXUfR_!!6000000000672-2-remus.png", "isHot": "false", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i4/6000000000758/O1CN01WFQ63R1HTDCuroLck_!!6000000000758-2-remus.png", "isHot": "false", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i3/6000000004095/O1CN01EVGVTJ1g7YsUnYMCM_!!6000000004095-2-remus.png", "isHot": "false", "isSelected": "false", "propPath": ""}]}}, "detail3Price": {"tag": "detail3Price", "type": "dinamicx$detail_v3_price$0$85", "fields": {"extraDiscount": {}, "price": {"priceUnit": " "}}}, "detail3Title": {"tag": "detail3Title", "type": "dinamicx$detail_v3_title$0$71", "fields": {"disablePrefetch": "true", "expanded": "false", "maxLine": "2", "title": [{"imageUrl": "https://gw.alicdn.com/imgextra/i1/O1CN01VD9Iap25oweneR31D_!!6000000007574-2-tps-120-60.png", "style": {"size": "16", "tailIndent": "false"}, "type": "image"}, {"imageUrl": "https://img.alicdn.com/imgextra/i2/O1CN01oJj7Zq1o4pMcjLDfc_!!6000000005172-2-tps-1-1.png", "style": {"size": "0.5", "tailIndent": "false"}, "type": "image"}, {"style": {"size": "16", "tailIndent": "false"}, "type": "image"}, {"style": {"bold": "true", "color": "#111111", "size": "16", "tailIndent": "false"}, "text": " Apple/苹果 iPhone 16 Pro Max", "type": "text"}], "titleCopy": {"拷贝": {"content": "Apple/苹果 iPhone 16 Pro Max", "params": {"itemId": "833003275432"}, "usertrack": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "拷贝链接": {"content": "https://item.taobao.com/item.htm?id=833003275432", "params": {"itemId": "833003275432"}, "usertrack": "CopyLink"}}, "titleType": "normal"}}, "detail3ItemInfoAndCommentDivider": {"tag": "detail3ItemInfoAndCommentDivider", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "16", "paddingTop": "half"}}, "detail3Comment": {"tag": "detail3Comment", "type": "dinamicx$detail_v3_appraise$0$80", "fields": {"bizId": "rate", "group": {"items": [{"blackCardUserUrl": "//img.alicdn.com/tfs/TB1wrG1elv0gK0jSZKbXXbK2FXa-225-96.png", "content": "手感很丝滑，很流畅，色彩显示非常不错，没有断触的情况出现，就是续航有点略差。通话音质拍照都很不错。", "dateTime": "2025-06-02", "feedId": "", "headPic": "//sns.m.taobao.com/avatar/sns/user/flag/sns_logo?type=taobao&kn=wwc_tb_11&bizCode=taobao_avatar&userFlag=RAzN84GK7wS8eNKrJNVuWD4dgGzG9Lhh3zABFUpn5hdA9ickLnycCC9pLS9q8QyGZ9QdTMAkvAUq6w6ushHoRsq6rCs6U3xVvBSm6QZgU9nvMSNhQFLjbggLBv7z8r7ekSkSNVevMqLXnGFd4UKiQC3dyijnnN6KghjmaLh", "isVip": "true", "media": [{"imageUrl": "//gw.alicdn.com/bao/uploaded/i2/O1CN01dlugUx1nE9xu2896b_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}], "mediaSize": "1", "memberLevel": "8", "skuInfo": "机身颜色:沙漠色钛金属;存储容量:256GB", "tmallMemberLevel": "2", "userName": "凳子是只小绵羊", "userStarPic": ""}, {"blackCardUserUrl": "//img.alicdn.com/tfs/TB1wrG1elv0gK0jSZKbXXbK2FXa-225-96.png", "content": "刚拿到，感觉还不错，传输数据后和原来手机差不多，重量比我之前的14pm更轻，目前没有什么问题", "dateTime": "2025-05-23", "feedId": "", "headPic": "//sns.m.taobao.com/avatar/sns/user/flag/sns_logo?type=taobao&kn=wwc_tb_11&bizCode=taobao_avatar&userFlag=RAzN84GK7wS8eNKrJNVuWD4dgGzG9Lhh3zACYWBJNn5cPy1mYdRkod7uBDxaPFLAzYqt3FUestoNeFR2uqSZWWpRGkx44ah7rc2tNooPysy6wncEK9gJcyxQTDqdgPpVKJFHg7YA8HvRNddxhJ7aE2TgS9Gf9jRKeFDNYTK", "isVip": "true", "media": [{"imageUrl": "//gw.alicdn.com/bao/uploaded/i2/O1CN01qjgq8R1SqXDYOpy2N_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}], "mediaSize": "1", "memberLevel": "9", "skuInfo": "机身颜色:沙漠色钛金属;存储容量:1TB", "tmallMemberLevel": "0", "userName": "知画幻影", "userStarPic": ""}]}, "locatorId": "divisionRate", "pageTitle": "评价", "title": "评价(7万+)"}, "events": {"openFirstItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-Comments_industry", "args": {"feed_id": "", "model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia0": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia1": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openSecondItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-Comments_industry", "args": {"feed_id": "", "model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia2": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "keywordsClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-CommentsTag_industry", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia3": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "titleClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_all_industry", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "detail3UGCDivision": {"tag": "detail3UGCDivision", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "16"}}, "detail3ShopInfo": {"tag": "detail3ShopInfo", "type": "dinamicx$detail_v3_shop_card$0$17", "fields": {"bgColor": "#F9F9F9", "brandIcons": [{"imageUrl": "//gw.alicdn.com/imgextra/i1/O1CN01VD9Iap25oweneR31D_!!6000000007574-2-tps-120-60.png"}], "certIconConst": "http://gw.alicdn.com/tfs/TB1zgzmlZLJ8KJjy0FnXXcFDpXa-171-148.png", "divisionColor": "#EEEEEE", "entranceList": [{"action": "enterShopClick", "text": "进店逛逛", "textColor": "#000000"}, {"action": "allItemClick", "text": "全部宝贝", "textColor": "#000000"}], "icon": "https://img.alicdn.com/imgextra/i2/**********/O1CN01MoNvU822AEhT4545I_!!**********.png", "needMask": "false", "padding": "16", "relationShipUrl": "https://dinamicx.alibabausercontent.com/l_pub/ts_common_subscription_relationship_widget/1684395523233/ts_common_subscription_relationship_widget.zip", "relationShipVersion": "4", "title": "Apple Store 官方旗舰店", "titleStyle": {"textColor": "#000000"}}, "events": {"enterShopClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}, {"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "itemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "brandClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "allItemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}]}}, "divisionDesc": {"tag": "divisionDesc", "type": "native$division", "fields": {"bizId": "desc", "locatorId": "divisionDesc", "name": "divisionDesc", "payload": {"displayType": "text", "fgcolor": "0x666666", "iconUrl": "", "title": "宝贝详情"}, "type": "division"}}, "detailDescComp": {"tag": "detailDescComp", "type": "native$detaildesc", "fields": {"name": "detailDesc", "payload": {"itemId": "833003275432", "shopId": "107922698", "shrinkDesc": "false", "shrinkPriceInfo": "false", "taobaoDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=833003275432&descVersion=6.0&type=1&f=icoss!0833003275432!13675442425&sellerType=B", "taobaoPcDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=833003275432&descVersion=6.0&type=1&f=icoss!0833003275432!13675442425&sellerType=B", "userId": "**********"}, "style": "detailDesc", "type": "detailDesc"}, "events": {"actions": [{"type": "ut_exposure", "fields": {"scm": "", "spm": "a2141.7631564.1999077549", "trackPage": "Page_Detail_Show_ProductDetail"}}]}}, "divisionDescRecmdComp": {"tag": "divisionDescRecmdComp", "type": "native$division", "fields": {"bizId": "looked", "locatorId": "divisionDescRecmd", "name": "divisionDescRecmd", "payload": {"bgcolor": "0xf4f4f4", "displayType": "gone", "height": "5", "iconUrl": "//img.alicdn.com/tps/TB1PGyPOVXXXXa8aXXXXXXXXXXX"}, "type": "division"}}, "guessYouLike": {"tag": "guessYouLike", "type": "native$guessyoulike", "fields": {"name": "guessYouLike", "payload": {"api": "mtop.taobao.wireless.home.awesome.itemdetail.recommend", "bizParams": {"m": "detail"}, "channel": "itemdetail", "itemId": "833003275432", "userId": "**********", "version": "1.0"}, "style": "shopRecommend", "type": "guessYouLike"}, "events": {"actions": [{"type": "ut_exposure", "fields": {"spm": "a2141.7631564.2737766", "trackPage": "Page_Detail_Show_Recommend"}}]}}, "detail3InclusiveBottomBar": {"tag": "detail3InclusiveBottomBar", "type": "dinamicx$detail_v3_inclusive_bottom_bar$0$9", "fields": {"_dObserveStates": {"isCollected": "init"}, "bizId": "bottomBar", "collectSummary": "false", "collected": "false", "detail3": "true", "height": "60", "leftButtons": [{"disabled": "true", "icon": {"color": "#ff5000", "iconFontName": "삊"}, "title": {"color": "#666666", "text": "店铺"}}, {"disabled": "true", "icon": {"color": "#666666", "iconFontName": "쁪"}, "title": {"color": "#666666", "text": "客服"}}, {"disabled": "true", "icon": {"color": "#666666", "iconFontName": "뀚"}, "title": {"color": "#666666", "text": "收藏"}}], "rightButtons": [{"background": {"disabledColor": ["#ffcb00", "#ff9402"], "gradientColor": ["#ffcb00", "#ff9402"]}, "bizCode": "addToCart", "disabled": "true", "title": "加入购物车", "titleStyle": {"color": "#ffffff"}, "widthRatio": "0.5"}, {"background": {"disabledColor": ["#ff7700", "#ff4900"], "gradientColor": ["#ff7700", "#ff4900"]}, "bizCode": "buy", "disabled": "true", "title": "立即购买", "titleStyle": {"color": "#ffffff"}, "widthRatio": "0.5"}]}}, "naviItemLeft": {"tag": "naviItemLeft", "type": "native$detailnaviitem", "fields": {"name": "naviItemLeft", "payload": {"accessHint": "返回", "positionKey": "left", "secondActions": [{"domain": "detail", "type": "go_detail_home"}], "titleSizeRatio": "0.375", "value": "ꁽ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "go_back"}]}}, "naviItemCustom": {"tag": "naviItemCustom", "type": "native$detailnaviitem", "fields": {"name": "naviItemCustom", "payload": {"accessHint": "购物车", "positionKey": "custom", "titleSizeRatio": "0.375", "value": "ꁊ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "user_track", "fields": {"trackName": "ShoppingCart", "trackNamePre": "Button-"}}, {"type": "open_url", "fields": {"url": "https://h5.m.taobao.com/awp/base/cart.htm", "urlParams": {"cartfrom": "detail", "itemId": "833003275432"}}}]}}, "naviItemRight": {"tag": "naviItemRight", "type": "native$detailnaviitem", "fields": {"name": "naviItemRight", "payload": {"accessHint": "更多", "positionKey": "right", "titleSizeRatio": "0.375", "value": "ꁪ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "show_menu"}]}}, "naviTabInfo": {"tag": "naviTabInfo", "type": "native$detailnavitabitem", "fields": {"name": "naviTabInfo", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "宝贝"}, "style": "tab", "targetBizId": "header", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "detailInfoAura"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailHome", "trackNamePre": "Button-"}}]}}, "naviTabRate": {"tag": "naviTabRate", "type": "native$detailnavitabitem", "fields": {"name": "naviTabRate", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "pageName": "Page_DetailComments", "secondActions": [{"domain": "detail", "type": "goto_rate_top"}], "title": "评价"}, "style": "tab", "targetBizId": "rate", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionRate"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailRate", "trackNamePre": "Button-"}}, {"type": "ut_exposure", "fields": {"spm": "a2141.7631564.2737664", "trackPage": "Page_Detail_Show_Detail"}}]}}, "naviTabDesc": {"tag": "naviTabDesc", "type": "native$detailnavitabitem", "fields": {"name": "naviTabDesc", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "详情"}, "style": "tab", "targetBizId": "desc", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionDesc"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailDesc", "trackNamePre": "Button-"}}]}}, "naviTabDescRecmd": {"tag": "naviTabDescRecmd", "type": "native$detailnavitabitem", "fields": {"name": "naviTabDescRecmd", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "推荐"}, "style": "tab", "targetBizId": "looked", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionDescRecmd"}}, {"type": "user_track", "fields": {"trackName": "GotoShopRecommend", "trackNamePre": "Button-"}}]}}, "detail3NaviItemBack": {"tag": "detail3NaviItemBack", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemBack", "payload": {"accessHint": "返回", "positionKey": "back", "value": "ꁽ"}, "type": "detailNaviItem"}}, "detail3NaviItemSearch": {"tag": "detail3NaviItemSearch", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemSearch", "payload": {"accessHint": "搜索", "positionKey": "search", "rightIndex": "0", "value": "끺"}, "type": "detailNaviItem"}}, "detail3NaviItemShare": {"tag": "detail3NaviItemShare", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemShare", "payload": {"accessHint": "分享", "positionKey": "share", "rightIndex": "1", "value": "ꄪ"}, "type": "detailNaviItem"}}, "detail3NaviItemCart": {"tag": "detail3NaviItemCart", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemCart", "payload": {"accessHint": "购物车", "jumpUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "positionKey": "cart", "rightIndex": "2", "value": "ꁊ"}, "type": "detailNaviItem"}}, "detail3NaviItemMore": {"tag": "detail3NaviItemMore", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemMore", "payload": {"accessHint": "更多", "positionKey": "more", "rightIndex": "3", "value": "ꁪ"}, "type": "detailNaviItem"}}, "detail3ElevatorAnchor": {"tag": "detail3ElevatorAnchor", "type": "native$detailelevatorfloat", "fields": {"data": {"expand": "true", "height": "133", "marginBottom": "122", "selectedLocationId": "detailInfoAura", "titles": [{"locationId": "detailInfoAura", "title": "宝贝"}, {"locationId": "divisionRate", "title": "评价"}, {"locationId": "divisionDesc", "title": "详情"}, {"locationId": "divisionDescRecmd", "title": "推荐"}], "width": "48"}, "template": {"name": "detail_v3_elevator_anchor", "url": "https://dinamicx.alibabausercontent.com/pub/detail_v3_elevator_anchor/1693993346457/detail_v3_elevator_anchor.zip", "version": "16"}, "type": "detail3ElevatorAnchor"}}}, "linkage": {"common": {"compress": "true"}, "signature": "375b89c4133e745588039bba6755f4d5"}, "hierarchy": {"root": "detail3", "structure": {"detail3": ["detailHome", "bottomBar", "naviBar", "ttNaviBar", "ttFloat"], "detailHome": ["detailInfoAura", "divisonDesc", "detailDesc", "divisionDescRecmd", "descRecmd"], "detailInfoAura": ["detailHeaderPic", "detail3TopLineSection", "detailV3SkuBarSection", "detail3PriceSection", "detail3TitleSection", "detail3InfoAndCommentDivider", "detail3CommentSection", "detail3UGCDivisionSection", "detail3ShopSection"], "detailHeaderPic": ["headerPic"], "headerPic": ["mainContainer"], "mainContainer": ["mainFrame", "progressArea", "locatorArea"], "mainFrame": ["frameGroup_video", "frameGroup_image"], "frameGroup_video": ["frame_video_0"], "frame_video_0": ["frameVideo_video_0"], "frameGroup_image": ["frame_pic_0", "frame_pic_1", "frame_pic_2", "frame_pic_3", "frame_pic_4", "frame_pic_5", "frame_pic_6", "frame_pic_7", "frame_pic_8", "frame_pic_9"], "frame_pic_0": ["frameImage_pic_0"], "frame_pic_1": ["frameImage_pic_1"], "frame_pic_2": ["frameImage_pic_2"], "frame_pic_3": ["frameImage_pic_3"], "frame_pic_4": ["frameImage_pic_4"], "frame_pic_5": ["frameImage_pic_5"], "frame_pic_6": ["frameImage_pic_6"], "frame_pic_7": ["frameImage_pic_7"], "frame_pic_8": ["frameImage_pic_8"], "frame_pic_9": ["frameImage_pic_9"], "progressArea": ["progressBar"], "locatorArea": ["locatorBar"], "locatorBar": ["locatorItem_video", "locatorItem_image"], "detail3TopLineSection": ["detail3TopLine"], "detailV3SkuBarSection": ["detailHeaderSKU"], "detail3PriceSection": ["detail3Price"], "detail3TitleSection": ["detail3Title"], "detail3InfoAndCommentDivider": ["detail3ItemInfoAndCommentDivider"], "detail3CommentSection": ["detail3Comment"], "detail3UGCDivisionSection": ["detail3UGCDivision"], "detail3ShopSection": ["detail3ShopInfo"], "divisonDesc": ["divisionDesc"], "detailDesc": ["detailDescComp"], "divisionDescRecmd": ["divisionDescRecmdComp"], "descRecmd": ["guessYouLike"], "bottomBar": ["detail3BottomBarSection"], "detail3BottomBarSection": ["detail3InclusiveBottomBar"], "naviBar": ["naviControl", "naviTabs"], "naviControl": ["naviItemLeft", "naviItemCustom", "naviItemRight"], "naviTabs": ["naviTabInfo", "naviTabRate", "naviTabDesc", "naviTabDescRecmd"], "ttNaviBar": ["detail3NaviItemBack", "detail3NaviItemSearch", "detail3NaviItemShare", "detail3NaviItemCart", "detail3NaviItemMore"], "ttFloat": ["detail3ElevatorAnchor"]}}, "endpoint": {"ultronage": "true", "protocolVersion": "4.0", "contextVersion": "taoDetailIndustry_gc_202506201635358962_328766", "domainCode": "taoDetailIndustry", "page": "detail3", "traceIds": ["213e037817509245757488436e1c4e"], "meta": {"template": {"id": "detail3$taoDetailIndustry_gc_202506201635358962_328766", "version": "9fc037c9537ee7e917a7981e5b2ce847", "cacheFields": ["container"]}}, "features": "2"}, "reload": "true", "components": {"native$headerbgimage": {"name": "headerbgimage", "type": "native", "url": "", "version": "0"}, "native$headervideo": {"name": "headervideo", "type": "native", "url": "", "version": "0"}}, "model": {"headerPic": {"dragEnd": {"events": {"dragEvent": [{"type": "locatorTo", "fields": {"locatorId": "desc"}}, {"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-ItemDetailRecommend", "args": {}, "eventId": "2201", "page": "Page_Detail"}}]}, "fields": {"position": "right", "pullText": "滑\n动\n发\n现\n更\n多\n内\n容", "releaseText": "释\n放\n发\n现\n更\n多\n内\n容"}}, "feature": {"disableSyncPropWithSku": "true"}, "groups": [{"id": "frameGroup_video", "items": [{"content": {"fields": {"code": "video_0", "contentStyle": {"paddingBottom": "0"}, "subType": "override", "thumbnailDimension": "1:1", "thumbnailUrl": "http://img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg", "type": "video", "videoId": "518219801607", "videoUrl": "http://cloud.video.taobao.com/play/u/**********/p/2/e/6/t/1/518219801607.mp4?appKey=38829"}, "id": "frameVideo_video_0", "type": "native$headervideo"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_video_0", "tagName": "宝贝讲解"}], "locatorId": "video"}, {"id": "frameGroup_image", "items": [{"content": {"fields": {"type": "image", "code": "pic_0", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg"}, "id": "frameImage_pic_0", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_0"}, {"content": {"fields": {"type": "image", "code": "pic_1", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i3/**********/O1CN01KvB3ck22AElxdt3rS_!!**********.jpg"}, "id": "frameImage_pic_1", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_1"}, {"content": {"fields": {"type": "image", "code": "pic_2", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/**********/O1CN012bCS8Y22AEjrsfugv_!!**********.jpg"}, "id": "frameImage_pic_2", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_2"}, {"content": {"fields": {"type": "image", "code": "pic_3", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/**********/O1CN01SvXUvx22AEjpiAWf3_!!**********.jpg"}, "id": "frameImage_pic_3", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_3"}, {"content": {"fields": {"type": "image", "code": "pic_4", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/**********/O1CN01sGqrUc22AEoOjFxXd_!!**********.png"}, "id": "frameImage_pic_4", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_4"}, {"content": {"fields": {"type": "image", "code": "pic_5", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/**********/O1CN01Kxymgn22AEoKdUEfw_!!**********.png"}, "id": "frameImage_pic_5", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_5"}, {"content": {"fields": {"type": "image", "code": "pic_6", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/**********/O1CN01qj6Zdh22AEjrFCiyb_!!**********.jpg"}, "id": "frameImage_pic_6", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_6"}, {"content": {"fields": {"type": "image", "code": "pic_7", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/**********/O1CN01ZorvPj22AEjrrgP0n_!!**********.jpg"}, "id": "frameImage_pic_7", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_7"}, {"content": {"fields": {"type": "image", "code": "pic_8", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/**********/O1CN017Bc4x022AEjvq7PMA_!!**********.jpg"}, "id": "frameImage_pic_8", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_8"}, {"content": {"fields": {"type": "image", "code": "pic_9", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/**********/O1CN01ODOrSV22AEjv5x5DP_!!**********.png"}, "id": "frameImage_pic_9", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_9"}], "locatorId": "image"}], "locators": [{"id": "video", "name": "视频"}, {"id": "image", "name": "图集"}], "properties": {}, "smallWindow": []}}, "global": {"data": {"bizData": {"useClientEngine": "true"}, "debug": {"detail4TraceInfo": ["Detail4RuleDegrade~apple"], "fromStatic": "true", "host": "taodetail033040050011.unsh.ea119@***********", "industryTraceInfo": [], "traceId": "213e037817509245757488436e1c4e"}, "detailClientData": {"detailClientOpt": {"guessYouLikeServerless": "true", "groupId": "0"}}, "detailOpt": {"cacheTimeToLive": "14400", "detailVersion": "industry"}, "diversion": {"detailTopSearch": {"url": "https://s.m.taobao.com/h5entry?g_channelSrp=detail&placeholder=&showText=搜索宝贝&g_historyOn=true&g_csearchdoor_spm=a2141.13130650&launchMode=android_new_task"}}, "feature": {"isPadDevice": "false", "enableDiscountAnim": "true", "headerPicScrollCycle": "false", "industryMainPic": "true", "maskingProduct": "false", "finalUltron": "true", "guessYouLikeNewStyle": "true", "delMainPicEvent": "true", "detail3VideoAutoPlay": "false", "commonHiddenItemRecmd": "false", "multiMerchantProduct": "false", "hintInsideCartButton": "false", "enablePriceAnim": "true", "detail3": "true", "preloadDetail": "true", "detail3HeadPic": "true", "forceNaviAlpha": "true", "hintDetail3BottomBar": "true", "descNewStyle": "true"}, "ignore": {"ignoreAll": "true"}, "item": {"containerDimension": "1:1", "images": ["https://img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg", "https://img.alicdn.com/imgextra/i3/**********/O1CN01KvB3ck22AElxdt3rS_!!**********.jpg", "https://img.alicdn.com/imgextra/i4/**********/O1CN012bCS8Y22AEjrsfugv_!!**********.jpg", "https://img.alicdn.com/imgextra/i4/**********/O1CN01SvXUvx22AEjpiAWf3_!!**********.jpg", "https://img.alicdn.com/imgextra/i2/**********/O1CN01sGqrUc22AEoOjFxXd_!!**********.png", "https://img.alicdn.com/imgextra/i1/**********/O1CN01Kxymgn22AEoKdUEfw_!!**********.png", "https://img.alicdn.com/imgextra/i1/**********/O1CN01qj6Zdh22AEjrFCiyb_!!**********.jpg", "https://img.alicdn.com/imgextra/i2/**********/O1CN01ZorvPj22AEjrrgP0n_!!**********.jpg", "https://img.alicdn.com/imgextra/i1/**********/O1CN017Bc4x022AEjvq7PMA_!!**********.jpg", "https://img.alicdn.com/imgextra/i2/**********/O1CN01ODOrSV22AEjv5x5DP_!!**********.png"], "title": "Apple/苹果 iPhone 16 Pro Max", "videos": [{"spatialVideoDimension": "1:1", "type": "2", "url": "https://cloud.video.taobao.com/play/u/**********/p/2/e/6/t/1/518219801607.mp4?appKey=38829", "videoId": "518219801607", "videoThumbnailURL": "https://img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg", "weexRecommendUrl": "https://market.m.taobao.com/apps/market/detailrax/recommend-items.html?spm=a2116h.app.0.0.16d957e9U2bxVj&wh_weex=true&itemId=833003275432"}]}, "pageLayout": {}, "trade": {"cartJumpUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "isBanSale4Oversea": "false", "isWap": "false", "useWap": "false"}, "useClientEngine": "true"}}}, "name": "esi"}], "feature": {"isOnLine": "true", "tcloudToH5": "true"}, "item": {"brandValueId": "30111", "businessId": "default", "cartUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "categoryId": "1512", "commentCount": "0", "countMultiple": [], "exParams": {}, "favcount": "327706", "h5ItemUrl": "https://new.m.taobao.com/detail.htm?id=833003275432&hybrid=true", "images": ["//img.alicdn.com/imgextra/i4/**********/O1CN01rVCl5L22AEocMuAyO_!!4611686018427384103-0-item_pic.jpg", "//img.alicdn.com/imgextra/i3/**********/O1CN01KvB3ck22AElxdt3rS_!!**********.jpg", "//img.alicdn.com/imgextra/i4/**********/O1CN012bCS8Y22AEjrsfugv_!!**********.jpg", "//img.alicdn.com/imgextra/i4/**********/O1CN01SvXUvx22AEjpiAWf3_!!**********.jpg", "//img.alicdn.com/imgextra/i2/**********/O1CN01sGqrUc22AEoOjFxXd_!!**********.png", "//img.alicdn.com/imgextra/i1/**********/O1CN01Kxymgn22AEoKdUEfw_!!**********.png", "//img.alicdn.com/imgextra/i1/**********/O1CN01qj6Zdh22AEjrFCiyb_!!**********.jpg", "//img.alicdn.com/imgextra/i2/**********/O1CN01ZorvPj22AEjrrgP0n_!!**********.jpg", "//img.alicdn.com/imgextra/i1/**********/O1CN017Bc4x022AEjvq7PMA_!!**********.jpg", "//img.alicdn.com/imgextra/i2/**********/O1CN01ODOrSV22AEjv5x5DP_!!**********.png"], "itemId": "833003275432", "openDecoration": "false", "pcADescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=833003275432&descVersion=7.0&type=1&f=icoss!0833003275432!13675442425&sellerType=B", "rootCategoryId": "1512", "skuText": "请选择机身颜色 存储容量 ", "subtitle": "", "taobaoDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=833003275432&descVersion=6.0&type=1&f=icoss!0833003275432!13675442425&sellerType=B", "taobaoPcDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=833003275432&descVersion=6.0&type=1&f=icoss!0833003275432!13675442425&sellerType=B", "title": "Apple/苹果 iPhone 16 Pro Max", "tmallDescUrl": "//mdetail.tmall.com/templates/pages/desc?id=833003275432"}, "preload": "true"}}, "895636359641": {"data": {"apiStack": [{"data": {"container": {"data": [{"name": "detail_v3_appraise", "containerType": "dinamicx", "version": "80", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_appraise/1747624030479/detail_v3_appraise.zip", "md5": null, "type": ["dinamicx$detail_v3_appraise$0$80"]}, {"name": "detail_v3_askall", "containerType": "dinamicx", "version": "30", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_askall/1742784091937/detail_v3_askall.zip", "md5": null, "type": ["dinamicx$detail_v3_askall$0$30"]}, {"name": "detail_v3_inclusive_bottom_bar", "containerType": "dinamicx", "version": "9", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_inclusive_bottom_bar/1748919314991/detail_v3_inclusive_bottom_bar.zip", "md5": null, "type": ["dinamicx$detail_v3_inclusive_bottom_bar$0$9"]}, {"name": "detail_v3_part_division", "containerType": "dinamicx", "version": "12", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_part_division/1698657679576/detail_v3_part_division.zip", "md5": null, "type": ["dinamicx$detail_v3_part_division$0$12"]}, {"name": "detail_v3_shop_card", "containerType": "dinamicx", "version": "17", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_shop_card/1694587183544/detail_v3_shop_card.zip", "md5": null, "type": ["dinamicx$detail_v3_shop_card$0$17"]}, {"name": "detail_v3_sku_picker", "containerType": "dinamicx", "version": "14", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_sku_picker/1749436153470/detail_v3_sku_picker.zip", "md5": null, "type": ["dinamicx$detail_v3_sku_picker$0$14"]}, {"name": "detail_v4_params_default", "containerType": "dinamicx", "version": "2", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_params_default/1745811048247/detail_v4_params_default.zip", "md5": null, "type": ["dinamicx$detail_v4_params_default$0$2"]}, {"name": "detail_v4_price", "containerType": "dinamicx", "version": "9", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_price/1748395728304/detail_v4_price.zip", "md5": null, "type": ["dinamicx$detail_v4_price$0$9"]}, {"name": "detail_v4_title", "containerType": "dinamicx", "version": "11", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_title/1750053988156/detail_v4_title.zip", "md5": null, "type": ["dinamicx$detail_v4_title$0$11"]}, {"name": "detail_v4_top_line", "containerType": "dinamicx", "version": "1", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_top_line/1746590069925/detail_v4_top_line.zip", "md5": null, "type": ["dinamicx$detail_v4_top_line$0$1"]}, {"name": "linear", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$linear$0$0"]}, {"name": "locator", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$locator$0$0"]}, {"name": "overlay", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$overlay$0$0"]}, {"name": "sticky", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$sticky$0$0"]}]}, "data": {"headerPic": {"tag": "headerPic", "type": "native$headerpic", "fields": {"dimension": "3:4", "independentAURANode": "true", "paddingBottom": "0"}}, "mainContainer": {"tag": "mainContainer", "type": "layout$linear$0$0", "fields": {"direction": "horizontal", "pullStyle": {"position": "right", "pullText": "滑\n动\n发\n现\n更\n多\n内\n容", "releaseText": "释\n放\n发\n现\n更\n多\n内\n容"}, "scrollMode": "page", "stretchHeight": "58"}, "events": {"dragEvent": [{"type": "locatorTo", "fields": {"locatorId": "desc"}}, {"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-ItemDetailRecommend", "args": {}, "eventId": "2201", "page": "Page_Detail"}}]}}, "frameGroup_image": {"tag": "frameGroup", "id": "image", "type": "layout$locator$0$0", "fields": {"locatorComponent": ["locatorBar", "progressBar"], "locatorId": "image"}}, "frame_pic_0": {"tag": "frame", "id": "pic_0", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_0": {"tag": "frameImage", "id": "pic_0", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_0", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/686947088/O1CN01oSe97W22EMNjHca0G_!!4611686018427386640-0-item_pic.jpg"}}, "frame_pic_1": {"tag": "frame", "id": "pic_1", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_1": {"tag": "frameImage", "id": "pic_1", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_1", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i3/686947088/O1CN01l676ms22EMJgFfBM1_!!686947088.jpg"}}, "frame_pic_2": {"tag": "frame", "id": "pic_2", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_2": {"tag": "frameImage", "id": "pic_2", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_2", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/686947088/O1CN01E6DgDJ22EMJgIZQRe_!!686947088.jpg"}}, "frame_pic_3": {"tag": "frame", "id": "pic_3", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_3": {"tag": "frameImage", "id": "pic_3", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_3", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i3/686947088/O1CN01rKd9v822EMJi8QAdW_!!686947088.jpg"}}, "frame_pic_4": {"tag": "frame", "id": "pic_4", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_4": {"tag": "frameImage", "id": "pic_4", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_4", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/686947088/O1CN01EHUwX522EMJpe569R_!!686947088.jpg"}}, "progressArea": {"tag": "progressArea", "type": "layout$sticky$0$0", "fields": {"marginBottom": "0", "marginTop": "-0", "position": "floatBottom", "showType": "showAlways"}}, "progressBar": {"tag": "progressBar", "type": "native$progressbar", "fields": {"contentStyle": {"paddingBottom": "0"}, "frameTag": {}}}, "locatorArea": {"tag": "locatorArea", "type": "layout$sticky$0$0", "fields": {"marginTop": "0", "position": "floatTop", "showType": "showAlways"}}, "locatorBar": {"tag": "locatorBar", "type": "native$locatorbar", "fields": {"hideLocator": "true", "isAURALeafNode": "true", "style": {"backgroundColor": "#4D242424", "initShowMaxCount": "3", "selectedAnchorBackgroundColor": "#ffffff", "selectedTextColor": "#000000", "unselectedTextColor": "#FFFFFF"}}, "events": {"exposureItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Show_DetailLocator_industry", "args": {"item_id": "895636359641", "seller_id": "686947088", "shop_id": "65829630", "spm": "a2141.7631564.detaillocator"}, "eventId": "2201", "page": "Page_Detail"}}]}}, "locatorItem_image": {"tag": "locatorItem", "id": "image", "type": "native$locatoritem", "fields": {"fixed": "false", "locator": "image", "text": "图集"}, "events": {"itemClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_DetailLocator_industry", "args": {"bizCode": "image", "item_id": "895636359641", "seller_id": "686947088", "shop_id": "65829630"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "detail4SkuPicker": {"tag": "detail4SkuPicker", "type": "dinamicx$detail_v3_sku_picker$0$14", "fields": {"_dClip": "false", "_dHeight": "58", "_dMarginBottom": "0", "_dMarginTop": "0", "_dObserveStates": {"galleryFrameGroupId": "frameGroup_image", "propPath": "0"}, "_dZIndex": "100", "selectedIndex": "\"1\"", "skuContents": [{"img": "https://img.alicdn.com/imgextra/i1/686947088/O1CN01mursDU22EMLweoUDq_!!686947088.png", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i2/686947088/O1CN01wRNMSt22EMLxV7jzW_!!686947088.png", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i4/686947088/O1CN01mJ1Gkv22EMLwsDCk1_!!686947088.png", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i3/686947088/O1CN01zzMpLc22EMLwuQZcL_!!686947088.png", "isSelected": "false", "propPath": ""}], "thumbList": [{"frameGroupIds": ["frameGroup_image"], "ratio": "1.0", "title": "图集", "url": "http://img.alicdn.com/imgextra/i4/686947088/O1CN01oSe97W22EMNjHca0G_!!4611686018427386640-0-item_pic.jpg"}]}}, "detail4TopLine": {"tag": "detail4TopLine", "type": "dinamicx$detail_v4_top_line$0$1"}, "detail4Price": {"tag": "detail4Price", "type": "dinamicx$detail_v4_price$0$9", "fields": {"bizId": "price", "disablePrefetch": "true", "enableDiscountAnim": "false", "enablePriceAnim": "true", "price": {"priceUnit": " "}}}, "detail4Title": {"tag": "detail4Title", "type": "dinamicx$detail_v4_title$0$11", "fields": {"titleCopy": {"拷贝": {"content": "【官方正品/顺丰速发】Apple/苹果 iPhone 16 Pro Max 新款5G手机国行全新原封官方网正品旗舰活动直降", "params": {"itemId": "895636359641"}, "usertrack": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "拷贝链接": {"content": "https://item.taobao.com/item.htm?id=895636359641", "params": {"itemId": "895636359641"}, "usertrack": "CopyLink"}}, "titleOld": [{"imageUrl": "https://gw.alicdn.com/imgextra/i1/O1CN01VD9Iap25oweneR31D_!!6000000007574-2-tps-120-60.png", "type": "image"}, {"text": " 【官方正品/顺丰速发】Apple/苹果 iPhone 16 Pro Max 新款5G手机国行全新原封官方网正品旗舰活动直降", "type": "text"}], "titleType": "normal"}}, "detail4FirstDividingLine": {"tag": "detail4FirstDividingLine", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "8", "paddingTop": "full"}}, "detail4ArgumentTopLine": {"tag": "detail4ArgumentTopLine", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#FFFFFF", "height": "2"}}, "detail4ParamsDefault": {"tag": "detail4ParamsDefault", "type": "dinamicx$detail_v4_params_default$0$2", "fields": {"fullLine": "false", "keywords": ["上市时间", "售后服务", "是否支持无线充电", "保修期", "耳机插头类型", "操作系统", "前置摄像头像素", "电池容量", "超广角像素", "有线充电功率", "解锁方式", "分辨率", "存储容量", "无线充电功率", "电信设备进网许可证编号", "机身颜色", "品牌", "Apple型号", "是否支持NFC", "CPU品牌", "屏幕刷新率", "CPU核心数", "最大光圈", "屏幕材质", "接口类型", "版本类型", "屏幕尺寸", "主摄像素", "CPU型号", "蓝牙版本", "长焦像素", "后壳材质", "3C证书编号"], "showBottomLine": "false"}, "events": {"itemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}]}}, "detail4ItemInfoAndCommentDivider": {"tag": "detail4ItemInfoAndCommentDivider", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "4", "paddingTop": "half"}}, "detail3Comment": {"tag": "detail3Comment", "type": "dinamicx$detail_v3_appraise$0$80", "fields": {"bizId": "rate", "group": {"items": [{"blackCardUserUrl": "", "content": "安全下车，虽然屏幕和后置摄像头是最差的，但是机身和屏幕无暇已经是理想中的情况了，电池也是今年3月份生产的不是库存机。这家店还是非常靠谱的。", "dateTime": "2025-06-20", "feedId": "", "headPic": "//sns.m.taobao.com/avatar/sns/user/flag/sns_logo?type=taobao&kn=wwc_tb_11&bizCode=taobao_avatar&userFlag=RAzN84GK7wS8eNJcTNm3cYQVbr2yw1dYqAz37kmcyf7eULC2FgMRvBcgTJ1QUh7U1Jv8svF1zbpjVrMpiD4H5oKUw9YWVuBfCQt1GSGtdALCyGRYNfPQbZmuFbmBPtE1aoefN25TWk53nQAyjzV9vDnTMcY7Ys3GHwJpFb7", "isVip": "false", "media": [{"imageUrl": "//gw.alicdn.com/bao/uploaded/i3/O1CN01IftCcv1Gai9SA1LGy_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i2/O1CN01YcqR4k1Gai9Ukc9b2_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i2/O1CN01GXXcmy1Gai9SeisI9_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}], "mediaSize": "3", "memberLevel": "0", "skuInfo": "机身颜色:白色钛金属;存储容量:256GB", "tmallMemberLevel": "0", "userName": "水木年华", "userStarPic": ""}, {"blackCardUserUrl": "//img.alicdn.com/tfs/TB1wrG1elv0gK0jSZKbXXbK2FXa-225-96.png", "content": "以前买手机总是首发去直营店买原价要贵好多，今年16pro max刚好在猫平台有做活动比原价便宜2000多太划算了，这家店因为是苹果线上授权经销商买着安心些，首先手机收到检查了外观没任保问题的，激活后也没什么问题，手机用了很顺手相比15pro max续航要好的多，发热要轻些总体很满意，大家放心购买", "dateTime": "2025-05-24", "feedId": "", "headPic": "//img.alicdn.com/imgextra/i4/O1CN01GbZNxl26Vzotrjqli_!!6000000007668-2-tps-160-160.png", "isVip": "true", "media": [{"imageUrl": "//gw.alicdn.com/bao/uploaded/i2/O1CN01gbJWcm1kFaLfEkNQC_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i2/O1CN01zbZEUE1kFaLew4q4q_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i3/O1CN01krX0Uc1kFaLfpCyi4_!!*******************-0-tbbala.jpg", "type": "image", "videoUrl": ""}], "mediaSize": "3", "memberLevel": "8", "skuInfo": "机身颜色:白色钛金属;存储容量:256GB", "tmallMemberLevel": "3", "userName": "匿名买家", "userStarPic": ""}]}, "keywords": [{"attribute": "20261000-11", "count": "24", "type": "1", "word": "是正品"}, {"attribute": "20231001-11", "count": "15", "type": "1", "word": "价格很低"}, {"attribute": "20201000-11", "count": "14", "type": "1", "word": "物流速度很快"}, {"attribute": "20091007-11", "count": "8", "type": "1", "word": "是全新的"}, {"attribute": "20161007-11", "count": "2", "type": "1", "word": "通信音质棒"}, {"attribute": "20101007-11", "count": "2", "type": "1", "word": "续航给力"}], "locatorId": "divisionRate", "pageTitle": "评价", "title": "评价(200+)"}, "events": {"openFirstItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-Comments_industry", "args": {"feed_id": "", "model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia0": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia1": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openSecondItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-Comments_industry", "args": {"feed_id": "", "model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia2": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "keywordsClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-CommentsTag_industry", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia3": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "titleClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_all_industry", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "detail3AskAll": {"tag": "detail3AskAll", "type": "dinamicx$detail_v3_askall$0$30", "fields": {"group": {"items": [{"answerCountText": "1个回答", "askIcon": "//gw.alicdn.com/imgextra/i4/O1CN01kITAMA1tP2otdGDmR_!!6000000005893-2-tps-72-72.png", "askText": "是激活过的手机吗？", "askTextColor": "#000000"}, {"answerCountText": "1个回答", "askIcon": "//gw.alicdn.com/imgextra/i4/O1CN01kITAMA1tP2otdGDmR_!!6000000005893-2-tps-72-72.png", "askText": "哪里发货？发的什么快递呀？", "askTextColor": "#000000"}]}, "title": "问大家(26)"}, "events": {"itemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}]}}, "detail3UGCDivision": {"tag": "detail3UGCDivision", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "16"}}, "detail3ShopInfo": {"tag": "detail3ShopInfo", "type": "dinamicx$detail_v3_shop_card$0$17", "fields": {"bgColor": "#F9F9F9", "bgImage": "https://img.alicdn.com/imgextra/i2/686947088/O1CN01TAudJD22ELpYWJ6kt_!!686947088.jpg", "brandIcons": [{"imageUrl": "//gw.alicdn.com/imgextra/i1/O1CN01VD9Iap25oweneR31D_!!6000000007574-2-tps-120-60.png"}, {"imageUrl": "//gw.alicdn.com/tfs/TB1pHLqjbH1gK0jSZFwXXc7aXXa-368-52.png?getAvatar=avatar"}], "certIconConst": "http://gw.alicdn.com/tfs/TB1zgzmlZLJ8KJjy0FnXXcFDpXa-171-148.png", "divisionColor": "#1FFFFFFF", "entranceList": [{"action": "enterShopClick", "text": "进店逛逛", "textColor": "#FFFFFF"}, {"action": "allItemClick", "text": "全部宝贝", "textColor": "#FFFFFF"}], "evaluates": [{"score": "4.7", "scoreTextColor": "#FFFFFF", "title": "宝贝描述", "titleColor": "#B8FFFFFF", "type": "desc"}, {"score": "4.7", "scoreTextColor": "#FFFFFF", "title": "卖家服务", "titleColor": "#B8FFFFFF", "type": "serv"}, {"score": "4.8", "scoreTextColor": "#FFFFFF", "title": "物流服务", "titleColor": "#B8FFFFFF", "type": "post"}], "icon": "https://img.alicdn.com/imgextra/i2/686947088/O1CN019445hz22EMBECmzAD_!!686947088-0-shopmanager.jpg", "needMask": "true", "padding": "16", "relationShipUrl": "https://dinamicx.alibabausercontent.com/l_pub/ts_common_subscription_relationship_widget/1684395523233/ts_common_subscription_relationship_widget.zip", "relationShipVersion": "4", "title": "壹品良机旗舰店", "titleStyle": {"textColor": "#FFFFFF"}}, "events": {"enterShopClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}, {"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "itemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "brandClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "allItemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}]}}, "divisionDesc": {"tag": "divisionDesc", "type": "native$division", "fields": {"bizId": "desc", "locatorId": "divisionDesc", "name": "divisionDesc", "payload": {"displayType": "text", "fgcolor": "0x666666", "iconUrl": "", "title": "宝贝详情"}, "type": "division"}}, "detailDescComp": {"tag": "detailDescComp", "type": "native$detaildesc", "fields": {"name": "detailDesc", "payload": {"itemId": "895636359641", "shopId": "65829630", "shrinkDesc": "false", "shrinkPriceInfo": "false", "taobaoDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=895636359641&descVersion=6.0&type=1&f=icoss!0895636359641!***********&sellerType=B", "taobaoPcDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=895636359641&descVersion=6.0&type=1&f=icoss!0895636359641!***********&sellerType=B", "userId": "686947088"}, "style": "detailDesc", "type": "detailDesc"}, "events": {"actions": [{"type": "ut_exposure", "fields": {"scm": "", "spm": "a2141.7631564.1999077549", "trackPage": "Page_Detail_Show_ProductDetail"}}]}}, "divisionDescRecmdComp": {"tag": "divisionDescRecmdComp", "type": "native$division", "fields": {"bizId": "looked", "locatorId": "divisionDescRecmd", "name": "divisionDescRecmd", "payload": {"bgcolor": "0xf4f4f4", "displayType": "gone", "height": "5", "iconUrl": "//img.alicdn.com/tps/TB1PGyPOVXXXXa8aXXXXXXXXXXX"}, "type": "division"}}, "guessYouLike": {"tag": "guessYouLike", "type": "native$guessyoulike", "fields": {"name": "guessYouLike", "payload": {"api": "mtop.taobao.wireless.home.awesome.itemdetail.recommend", "bizParams": {"m": "detail"}, "channel": "itemdetail", "itemId": "895636359641", "userId": "686947088", "version": "1.0"}, "style": "shopRecommend", "type": "guessYouLike"}, "events": {"actions": [{"type": "ut_exposure", "fields": {"spm": "a2141.7631564.2737766", "trackPage": "Page_Detail_Show_Recommend"}}]}}, "detail3InclusiveBottomBar": {"tag": "detail3InclusiveBottomBar", "type": "dinamicx$detail_v3_inclusive_bottom_bar$0$9", "fields": {"_dObserveStates": {"isCollected": "init"}, "bizId": "bottomBar", "collectSummary": "false", "collected": "false", "detail3": "true", "height": "60", "leftButtons": [{"disabled": "true", "icon": {"color": "#ff5000", "iconFontName": "삊"}, "title": {"color": "#666666", "text": "店铺"}}, {"disabled": "true", "icon": {"color": "#666666", "iconFontName": "쁪"}, "title": {"color": "#666666", "text": "客服"}}, {"disabled": "true", "icon": {"color": "#666666", "iconFontName": "뀚"}, "title": {"color": "#666666", "text": "收藏"}}], "rightButtons": [{"background": {"disabledColor": ["#ffcb00", "#ff9402"], "gradientColor": ["#ffcb00", "#ff9402"]}, "bizCode": "addToCart", "disabled": "true", "title": "加入购物车", "titleStyle": {"color": "#ffffff"}, "widthRatio": "0.5"}, {"background": {"disabledColor": ["#ff7700", "#ff4900"], "gradientColor": ["#ff7700", "#ff4900"]}, "bizCode": "buy", "disabled": "true", "title": "立即购买", "titleStyle": {"color": "#ffffff"}, "widthRatio": "0.5"}]}}, "naviItemLeft": {"tag": "naviItemLeft", "type": "native$detailnaviitem", "fields": {"name": "naviItemLeft", "payload": {"accessHint": "返回", "positionKey": "left", "secondActions": [{"domain": "detail", "type": "go_detail_home"}], "titleSizeRatio": "0.375", "value": "ꁽ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "go_back"}]}}, "naviItemCustom": {"tag": "naviItemCustom", "type": "native$detailnaviitem", "fields": {"name": "naviItemCustom", "payload": {"accessHint": "购物车", "positionKey": "custom", "titleSizeRatio": "0.375", "value": "ꁊ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "user_track", "fields": {"trackName": "ShoppingCart", "trackNamePre": "Button-"}}, {"type": "open_url", "fields": {"url": "https://h5.m.taobao.com/awp/base/cart.htm", "urlParams": {"cartfrom": "detail", "itemId": "895636359641"}}}]}}, "naviItemRight": {"tag": "naviItemRight", "type": "native$detailnaviitem", "fields": {"name": "naviItemRight", "payload": {"accessHint": "更多", "positionKey": "right", "titleSizeRatio": "0.375", "value": "ꁪ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "show_menu"}]}}, "naviTabInfo": {"tag": "naviTabInfo", "type": "native$detailnavitabitem", "fields": {"name": "naviTabInfo", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "宝贝"}, "style": "tab", "targetBizId": "header", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "detailInfoAura"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailHome", "trackNamePre": "Button-"}}]}}, "naviTabRate": {"tag": "naviTabRate", "type": "native$detailnavitabitem", "fields": {"name": "naviTabRate", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "pageName": "Page_DetailComments", "secondActions": [{"domain": "detail", "type": "goto_rate_top"}], "title": "评价"}, "style": "tab", "targetBizId": "rate", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionRate"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailRate", "trackNamePre": "Button-"}}, {"type": "ut_exposure", "fields": {"spm": "a2141.7631564.2737664", "trackPage": "Page_Detail_Show_Detail"}}]}}, "naviTabDesc": {"tag": "naviTabDesc", "type": "native$detailnavitabitem", "fields": {"name": "naviTabDesc", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "详情"}, "style": "tab", "targetBizId": "desc", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionDesc"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailDesc", "trackNamePre": "Button-"}}]}}, "naviTabDescRecmd": {"tag": "naviTabDescRecmd", "type": "native$detailnavitabitem", "fields": {"name": "naviTabDescRecmd", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "推荐"}, "style": "tab", "targetBizId": "looked", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionDescRecmd"}}, {"type": "user_track", "fields": {"trackName": "GotoShopRecommend", "trackNamePre": "Button-"}}]}}, "detail3NaviItemBack": {"tag": "detail3NaviItemBack", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemBack", "payload": {"accessHint": "返回", "positionKey": "back", "value": "ꁽ"}, "type": "detailNaviItem"}}, "detail3NaviItemSearch": {"tag": "detail3NaviItemSearch", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemSearch", "payload": {"accessHint": "搜索", "positionKey": "search", "rightIndex": "0", "value": "끺"}, "type": "detailNaviItem"}}, "detail3NaviItemShare": {"tag": "detail3NaviItemShare", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemShare", "payload": {"accessHint": "分享", "positionKey": "share", "rightIndex": "1", "value": "ꄪ"}, "type": "detailNaviItem"}}, "detail3NaviItemCart": {"tag": "detail3NaviItemCart", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemCart", "payload": {"accessHint": "购物车", "jumpUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "positionKey": "cart", "rightIndex": "2", "value": "ꁊ"}, "type": "detailNaviItem"}}, "detail3NaviItemMore": {"tag": "detail3NaviItemMore", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemMore", "payload": {"accessHint": "更多", "positionKey": "more", "rightIndex": "4", "value": "ꁪ"}, "type": "detailNaviItem"}}, "detail3ElevatorAnchor": {"tag": "detail3ElevatorAnchor", "type": "native$detailelevatorfloat", "fields": {"data": {"expand": "true", "height": "133", "marginBottom": "122", "selectedLocationId": "detailInfoAura", "titles": [{"locationId": "detailInfoAura", "title": "宝贝"}, {"locationId": "divisionRate", "title": "评价"}, {"locationId": "divisionDesc", "title": "详情"}, {"locationId": "divisionDescRecmd", "title": "推荐"}], "width": "48"}, "template": {"name": "detail_v3_elevator_anchor", "url": "https://dinamicx.alibabausercontent.com/pub/detail_v3_elevator_anchor/1693993346457/detail_v3_elevator_anchor.zip", "version": "16"}, "type": "detail3ElevatorAnchor"}}}, "linkage": {"common": {"compress": "true"}, "signature": "27eaab14479387ca6346c70f57dc98c5"}, "hierarchy": {"root": "detail4", "structure": {"detail4": ["detailHome", "bottomBar", "naviBar", "ttNaviBar", "ttFloat"], "detailHome": ["detailInfoAura", "divisonDesc", "detailDesc", "divisionDescRecmd", "descRecmd"], "detailInfoAura": ["detailHeaderPic", "detail4SkuPickerSection", "detail4TopLineSection", "detail4PriceSection", "detail4TitleSection", "detail4FirstDividingLineSection", "detail4ArgumentSection", "detail4ParamsSection", "detail4InfoAndCommentDivider", "detail3CommentSection", "detail3AskAllSection", "detail3UGCDivisionSection", "detail4ShopSection"], "detailHeaderPic": ["headerPic"], "headerPic": ["mainContainer"], "mainContainer": ["mainFrame", "progressArea", "locatorArea"], "mainFrame": ["frameGroup_image"], "frameGroup_image": ["frame_pic_0", "frame_pic_1", "frame_pic_2", "frame_pic_3", "frame_pic_4"], "frame_pic_0": ["frameImage_pic_0"], "frame_pic_1": ["frameImage_pic_1"], "frame_pic_2": ["frameImage_pic_2"], "frame_pic_3": ["frameImage_pic_3"], "frame_pic_4": ["frameImage_pic_4"], "progressArea": ["progressBar"], "locatorArea": ["locatorBar"], "locatorBar": ["locatorItem_image"], "detail4SkuPickerSection": ["detail4SkuPicker"], "detail4TopLineSection": ["detail4TopLine"], "detail4PriceSection": ["detail4Price"], "detail4TitleSection": ["detail4Title"], "detail4FirstDividingLineSection": ["detail4FirstDividingLine"], "detail4ArgumentSection": ["detail4ArgumentTopLine"], "detail4ParamsSection": ["detail4ParamsDefault"], "detail4InfoAndCommentDivider": ["detail4ItemInfoAndCommentDivider"], "detail3CommentSection": ["detail3Comment"], "detail3AskAllSection": ["detail3AskAll"], "detail3UGCDivisionSection": ["detail3UGCDivision"], "detail4ShopSection": ["detail3ShopInfo"], "divisonDesc": ["divisionDesc"], "detailDesc": ["detailDescComp"], "divisionDescRecmd": ["divisionDescRecmdComp"], "descRecmd": ["guessYouLike"], "bottomBar": ["detail3BottomBarSection"], "detail3BottomBarSection": ["detail3InclusiveBottomBar"], "naviBar": ["naviControl", "naviTabs"], "naviControl": ["naviItemLeft", "naviItemCustom", "naviItemRight"], "naviTabs": ["naviTabInfo", "naviTabRate", "naviTabDesc", "naviTabDescRecmd"], "ttNaviBar": ["detail3NaviItemBack", "detail3NaviItemSearch", "detail3NaviItemShare", "detail3NaviItemCart", "detail3NaviItemMore"], "ttFloat": ["detail3ElevatorAnchor"]}}, "endpoint": {"ultronage": "true", "protocolVersion": "4.0", "contextVersion": "taoDetailIndustry_gc_202506201635358962_328766", "domainCode": "taoDetailIndustry", "page": "detail4", "traceIds": ["213e037817509245757488436e1c4e"], "meta": {"template": {"id": "detail4$taoDetailIndustry_gc_202506201635358962_328766", "version": "6e2b0746b3178cde14fe548a254a9ec1", "cacheFields": ["container"]}}, "features": "2"}, "reload": "true", "components": {"native$headerbgimage": {"name": "headerbgimage", "type": "native", "url": "", "version": "0"}}, "model": {"headerPic": {"dragEnd": {"events": {"dragEvent": [{"type": "locatorTo", "fields": {"locatorId": "desc"}}, {"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-ItemDetailRecommend", "args": {}, "eventId": "2201", "page": "Page_Detail"}}]}, "fields": {"position": "right", "pullText": "滑\n动\n发\n现\n更\n多\n内\n容", "releaseText": "释\n放\n发\n现\n更\n多\n内\n容"}}, "feature": {"disableSyncPropWithSku": "true"}, "groups": [{"id": "frameGroup_image", "items": [{"content": {"fields": {"type": "image", "code": "pic_0", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/686947088/O1CN01oSe97W22EMNjHca0G_!!4611686018427386640-0-item_pic.jpg"}, "id": "frameImage_pic_0", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_0"}, {"content": {"fields": {"type": "image", "code": "pic_1", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i3/686947088/O1CN01l676ms22EMJgFfBM1_!!686947088.jpg"}, "id": "frameImage_pic_1", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_1"}, {"content": {"fields": {"type": "image", "code": "pic_2", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/686947088/O1CN01E6DgDJ22EMJgIZQRe_!!686947088.jpg"}, "id": "frameImage_pic_2", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_2"}, {"content": {"fields": {"type": "image", "code": "pic_3", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i3/686947088/O1CN01rKd9v822EMJi8QAdW_!!686947088.jpg"}, "id": "frameImage_pic_3", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_3"}, {"content": {"fields": {"type": "image", "code": "pic_4", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/686947088/O1CN01EHUwX522EMJpe569R_!!686947088.jpg"}, "id": "frameImage_pic_4", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_4"}], "locatorId": "image"}], "locators": [{"id": "image", "name": "图集"}], "mainPicBizData": {"rbAction": {"template": {"name": "detail_v4_gallery_mini_action", "version": "5", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_gallery_mini_action/1749436254191/detail_v4_gallery_mini_action.zip"}}}, "properties": {"stretchHeight": "58"}, "smallWindow": []}}, "global": {"data": {"bizData": {"useClientEngine": "true"}, "debug": {"fromStatic": "true", "host": "taodetail033040050011.unsh.ea119@***********", "industryTraceInfo": [], "traceId": "213e037817509245757488436e1c4e"}, "detailClientData": {"detailClientOpt": {"guessYouLikeServerless": "true", "groupId": "0"}}, "detailOpt": {"cacheTimeToLive": "14400", "detailVersion": "industry"}, "diversion": {"detailTopSearch": {"url": "https://s.m.taobao.com/h5entry?g_channelSrp=detail&placeholder=&showText=搜索宝贝&g_historyOn=true&g_csearchdoor_spm=a2141.13130650&launchMode=android_new_task"}}, "feature": {"isPadDevice": "false", "enableDiscountAnim": "true", "headerPicScrollCycle": "false", "industryMainPic": "true", "maskingProduct": "false", "finalUltron": "true", "guessYouLikeNewStyle": "true", "delMainPicEvent": "true", "commonHiddenItemRecmd": "false", "multiMerchantProduct": "false", "titleItemTagMix": "true", "hintInsideCartButton": "false", "enablePriceAnim": "true", "detail3": "true", "preloadDetail": "true", "detail3HeadPic": "true", "forceNaviAlpha": "true", "mainPicHideLocator": "true", "hintDetail3BottomBar": "true", "descNewStyle": "true"}, "ignore": {"ignoreAll": "true"}, "item": {"containerDimension": "1:1", "images": ["https://img.alicdn.com/imgextra/i4/686947088/O1CN01oSe97W22EMNjHca0G_!!4611686018427386640-0-item_pic.jpg", "https://img.alicdn.com/imgextra/i3/686947088/O1CN01l676ms22EMJgFfBM1_!!686947088.jpg", "https://img.alicdn.com/imgextra/i2/686947088/O1CN01E6DgDJ22EMJgIZQRe_!!686947088.jpg", "https://img.alicdn.com/imgextra/i3/686947088/O1CN01rKd9v822EMJi8QAdW_!!686947088.jpg", "https://img.alicdn.com/imgextra/i1/686947088/O1CN01EHUwX522EMJpe569R_!!686947088.jpg"], "title": "【官方正品/顺丰速发】Apple/苹果 iPhone 16 Pro Max 新款5G手机国行全新原封官方网正品旗舰活动直降"}, "pageLayout": {}, "trade": {"cartJumpUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "isBanSale4Oversea": "false", "isWap": "false", "useWap": "false"}, "useClientEngine": "true"}}}, "name": "esi"}], "feature": {"isOnLine": "true", "tcloudToH5": "true"}, "item": {"brandValueId": "30111", "businessId": "default", "cartUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "categoryId": "1512", "commentCount": "0", "countMultiple": [], "exParams": {}, "favcount": "3041", "h5ItemUrl": "https://new.m.taobao.com/detail.htm?id=895636359641&hybrid=true", "images": ["//img.alicdn.com/imgextra/i4/686947088/O1CN01oSe97W22EMNjHca0G_!!4611686018427386640-0-item_pic.jpg", "//img.alicdn.com/imgextra/i3/686947088/O1CN01l676ms22EMJgFfBM1_!!686947088.jpg", "//img.alicdn.com/imgextra/i2/686947088/O1CN01E6DgDJ22EMJgIZQRe_!!686947088.jpg", "//img.alicdn.com/imgextra/i3/686947088/O1CN01rKd9v822EMJi8QAdW_!!686947088.jpg", "//img.alicdn.com/imgextra/i1/686947088/O1CN01EHUwX522EMJpe569R_!!686947088.jpg"], "itemId": "895636359641", "openDecoration": "false", "pcADescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=895636359641&descVersion=7.0&type=1&f=icoss!0895636359641!***********&sellerType=B", "rootCategoryId": "1512", "skuText": "请选择机身颜色 存储容量 ", "subtitle": "", "taobaoDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=895636359641&descVersion=6.0&type=1&f=icoss!0895636359641!***********&sellerType=B", "taobaoPcDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=895636359641&descVersion=6.0&type=1&f=icoss!0895636359641!***********&sellerType=B", "title": "【官方正品/顺丰速发】Apple/苹果 iPhone 16 Pro Max 新款5G手机国行全新原封官方网正品旗舰活动直降", "tmallDescUrl": "//mdetail.tmall.com/templates/pages/desc?id=895636359641"}, "preload": "true"}}, "857780853397": {"data": {"apiStack": [{"data": {"container": {"data": [{"name": "detail_v3_appraise", "containerType": "dinamicx", "version": "80", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_appraise/1747624030479/detail_v3_appraise.zip", "md5": null, "type": ["dinamicx$detail_v3_appraise$0$80"]}, {"name": "detail_v3_askall", "containerType": "dinamicx", "version": "30", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_askall/1742784091937/detail_v3_askall.zip", "md5": null, "type": ["dinamicx$detail_v3_askall$0$30"]}, {"name": "detail_v3_inclusive_bottom_bar", "containerType": "dinamicx", "version": "9", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_inclusive_bottom_bar/1748919314991/detail_v3_inclusive_bottom_bar.zip", "md5": null, "type": ["dinamicx$detail_v3_inclusive_bottom_bar$0$9"]}, {"name": "detail_v3_part_division", "containerType": "dinamicx", "version": "12", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_part_division/1698657679576/detail_v3_part_division.zip", "md5": null, "type": ["dinamicx$detail_v3_part_division$0$12"]}, {"name": "detail_v3_shop_card", "containerType": "dinamicx", "version": "17", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_shop_card/1694587183544/detail_v3_shop_card.zip", "md5": null, "type": ["dinamicx$detail_v3_shop_card$0$17"]}, {"name": "detail_v3_sku_picker", "containerType": "dinamicx", "version": "14", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v3_sku_picker/1749436153470/detail_v3_sku_picker.zip", "md5": null, "type": ["dinamicx$detail_v3_sku_picker$0$14"]}, {"name": "detail_v4_params_default", "containerType": "dinamicx", "version": "2", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_params_default/1745811048247/detail_v4_params_default.zip", "md5": null, "type": ["dinamicx$detail_v4_params_default$0$2"]}, {"name": "detail_v4_price", "containerType": "dinamicx", "version": "9", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_price/1748395728304/detail_v4_price.zip", "md5": null, "type": ["dinamicx$detail_v4_price$0$9"]}, {"name": "detail_v4_title", "containerType": "dinamicx", "version": "11", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_title/1750053988156/detail_v4_title.zip", "md5": null, "type": ["dinamicx$detail_v4_title$0$11"]}, {"name": "detail_v4_top_line", "containerType": "dinamicx", "version": "1", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_top_line/1746590069925/detail_v4_top_line.zip", "md5": null, "type": ["dinamicx$detail_v4_top_line$0$1"]}, {"name": "linear", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$linear$0$0"]}, {"name": "locator", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$locator$0$0"]}, {"name": "overlay", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$overlay$0$0"]}, {"name": "sticky", "containerType": "layout", "version": "0", "url": null, "md5": null, "type": ["layout$sticky$0$0"]}]}, "data": {"headerPic": {"tag": "headerPic", "type": "native$headerpic", "fields": {"dimension": "3:4", "independentAURANode": "true", "paddingBottom": "0"}}, "mainContainer": {"tag": "mainContainer", "type": "layout$linear$0$0", "fields": {"direction": "horizontal", "pullStyle": {"position": "right", "pullText": "滑\n动\n发\n现\n更\n多\n内\n容", "releaseText": "释\n放\n发\n现\n更\n多\n内\n容"}, "scrollMode": "page", "stretchHeight": "58"}, "events": {"dragEvent": [{"type": "locatorTo", "fields": {"locatorId": "desc"}}, {"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-ItemDetailRecommend", "args": {}, "eventId": "2201", "page": "Page_Detail"}}]}}, "frameGroup_video": {"tag": "frameGroup", "id": "video", "type": "layout$locator$0$0", "fields": {"locatorComponent": ["locatorBar", "progressBar"], "locatorId": "video"}}, "frame_video_0": {"tag": "frame", "id": "video_0", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false", "tag": "宝贝讲解"}}, "frameVideo_video_0": {"tag": "frameVideo", "id": "video_0", "type": "native$headervideo", "fields": {"code": "video_0", "contentStyle": {"paddingBottom": "0"}, "subType": "override", "thumbnailDimension": "1:1", "thumbnailUrl": "http://img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg", "type": "video", "videoId": "520265096873", "videoUrl": "http://cloud.video.taobao.com/play/u/15785155/p/2/e/6/t/1/520265096873.mp4?appKey=38829"}}, "frameGroup_image": {"tag": "frameGroup", "id": "image", "type": "layout$locator$0$0", "fields": {"locatorComponent": ["locatorBar", "progressBar"], "locatorId": "image"}}, "frame_pic_0": {"tag": "frame", "id": "pic_0", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_0": {"tag": "frameImage", "id": "pic_0", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_0", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg"}}, "frame_pic_1": {"tag": "frame", "id": "pic_1", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_1": {"tag": "frameImage", "id": "pic_1", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_1", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/15785155/O1CN015pkcPr1nx2jVWa0CP_!!15785155.jpg"}}, "frame_pic_2": {"tag": "frame", "id": "pic_2", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_2": {"tag": "frameImage", "id": "pic_2", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_2", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/15785155/O1CN01dwgDra1nx2jXXPgMe_!!15785155.jpg"}}, "frame_pic_3": {"tag": "frame", "id": "pic_3", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_3": {"tag": "frameImage", "id": "pic_3", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_3", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/15785155/O1CN019R5rm91nx2jWoBsXY_!!15785155.jpg"}}, "frame_pic_4": {"tag": "frame", "id": "pic_4", "type": "layout$overlay$0$0", "fields": {"dimension": "1:1", "isSelected": "false", "showLightOff": "false"}}, "frameImage_pic_4": {"tag": "frameImage", "id": "pic_4", "type": "native$headerbgimage", "fields": {"type": "image", "code": "pic_4", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/15785155/O1CN01xDaHb01nx2jXcYJ4s_!!15785155.jpg"}}, "progressArea": {"tag": "progressArea", "type": "layout$sticky$0$0", "fields": {"marginBottom": "0", "marginTop": "-0", "position": "floatBottom", "showType": "showAlways"}}, "progressBar": {"tag": "progressBar", "type": "native$progressbar", "fields": {"contentStyle": {"paddingBottom": "0"}, "frameTag": {}}}, "locatorArea": {"tag": "locatorArea", "type": "layout$sticky$0$0", "fields": {"marginTop": "0", "position": "floatTop", "showType": "showAlways"}}, "locatorBar": {"tag": "locatorBar", "type": "native$locatorbar", "fields": {"hideLocator": "true", "isAURALeafNode": "true", "style": {"backgroundColor": "#4D242424", "initShowMaxCount": "3", "selectedAnchorBackgroundColor": "#ffffff", "selectedTextColor": "#000000", "unselectedTextColor": "#FFFFFF"}}, "events": {"exposureItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Show_DetailLocator_industry", "args": {"item_id": "857780853397", "seller_id": "15785155", "shop_id": "33278965", "spm": "a2141.7631564.detaillocator"}, "eventId": "2201", "page": "Page_Detail"}}]}}, "locatorItem_video": {"tag": "locatorItem", "id": "video", "type": "native$locatoritem", "fields": {"fixed": "false", "locator": "video", "text": "视频"}, "events": {"itemClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_DetailLocator_industry", "args": {"bizCode": "video", "item_id": "857780853397", "seller_id": "15785155", "shop_id": "33278965"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "locatorItem_image": {"tag": "locatorItem", "id": "image", "type": "native$locatoritem", "fields": {"fixed": "false", "locator": "image", "text": "图集"}, "events": {"itemClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_DetailLocator_industry", "args": {"bizCode": "image", "item_id": "857780853397", "seller_id": "15785155", "shop_id": "33278965"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "detail4SkuPicker": {"tag": "detail4SkuPicker", "type": "dinamicx$detail_v3_sku_picker$0$14", "fields": {"_dClip": "false", "_dHeight": "58", "_dMarginBottom": "0", "_dMarginTop": "0", "_dObserveStates": {"galleryFrameGroupId": "frameGroup_image", "propPath": "0"}, "_dZIndex": "100", "selectedIndex": "\"1\"", "skuContents": [{"img": "https://img.alicdn.com/imgextra/i1/15785155/O1CN01iZIgtl1nx2jOzyYEs_!!15785155.png", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i4/15785155/O1CN01EqqrE81nx2jXcRnDa_!!15785155.png", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i2/15785155/O1CN01IHYnYe1nx2jTLlLy8_!!15785155.png", "isSelected": "false", "propPath": ""}, {"img": "https://img.alicdn.com/imgextra/i3/15785155/O1CN01DiMq8D1nx2jOzxHFs_!!15785155.png", "isSelected": "false", "propPath": ""}], "thumbList": [{"frameGroupIds": ["frameGroup_image"], "ratio": "1.0", "title": "图集", "url": "http://img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg"}]}}, "detail4TopLine": {"tag": "detail4TopLine", "type": "dinamicx$detail_v4_top_line$0$1"}, "detail4Price": {"tag": "detail4Price", "type": "dinamicx$detail_v4_price$0$9", "fields": {"bizId": "price", "disablePrefetch": "true", "enableDiscountAnim": "false", "enablePriceAnim": "true", "price": {"priceUnit": " "}}}, "detail4Title": {"tag": "detail4Title", "type": "dinamicx$detail_v4_title$0$11", "fields": {"titleCopy": {"拷贝": {"content": "【16promax】Apple/苹果 iPhone 16 Pro Max 国行US版全网通手机", "params": {"itemId": "857780853397"}, "usertrack": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "拷贝链接": {"content": "https://item.taobao.com/item.htm?id=857780853397", "params": {"itemId": "857780853397"}, "usertrack": "CopyLink"}}, "titleOld": [{"type": "image"}, {"text": "【16promax】Apple/苹果 iPhone 16 Pro Max 国行US版全网通手机", "type": "text"}], "titleType": "normal"}}, "detail4FirstDividingLine": {"tag": "detail4FirstDividingLine", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "8", "paddingTop": "full"}}, "detail4ArgumentTopLine": {"tag": "detail4ArgumentTopLine", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#FFFFFF", "height": "2"}}, "detail4ParamsDefault": {"tag": "detail4ParamsDefault", "type": "dinamicx$detail_v4_params_default$0$2", "fields": {"fullLine": "false", "keywords": ["上市时间", "售后服务", "是否支持无线充电", "耳机插头类型", "操作系统", "套餐类型", "前置摄像头像素", "电池容量", "超广角像素", "有线充电功率", "解锁方式", "分辨率", "存储容量", "无线充电功率", "电信设备进网许可证编号", "机身颜色", "品牌", "Apple型号", "是否支持NFC", "CPU品牌", "屏幕刷新率", "CPU核心数", "最大光圈", "屏幕材质", "接口类型", "屏幕尺寸", "主摄像素", "CPU型号", "蓝牙版本", "长焦像素", "后壳材质", "3C证书编号"], "showBottomLine": "false"}, "events": {"itemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}]}}, "detail4ItemInfoAndCommentDivider": {"tag": "detail4ItemInfoAndCommentDivider", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "4", "paddingTop": "half"}}, "detail3Comment": {"tag": "detail3Comment", "type": "dinamicx$detail_v3_appraise$0$80", "fields": {"bizId": "rate", "group": {"items": [{"blackCardUserUrl": "", "content": "对于第一次使用卡贴机的人来说，真的很担惊受怕。因为怕出问题，。后来花了很多时间去了解卡贴机，也看了几次卖家店铺的评论。总体真实好评居多，终于还是选择下单。毕竟相比国行，性价比不算很高，国行7458-老板这里5450加50未激活。也少了2000。目前使用了一个星期。因为是电信卡的原因，一次都没有跳激活，和国行几乎没有区别。很好，很喜欢。所以6月3号又下单了一台512的白色。真实评论。。后续会追评", "dateTime": "2025-06-03", "feedId": "", "headPic": "//sns.m.taobao.com/avatar/sns/user/flag/sns_logo?type=taobao&kn=wwc_tb_11&bizCode=taobao_avatar&userFlag=RAzN83923zmuym8iR9sFYjYM9DEMcgUgiCbrPgNHvgMgZAYtgQDzWaHxWS2Pf1veaFmLouaiPZVRoQWoiuu5F7JgYeVqCK6G7s3NbZ6fEUy2idtds2U4HFrrjuiM", "isVip": "false", "media": [{"imageUrl": "//gw.alicdn.com/bao/uploaded/i1/O1CN01v7li6X2Ka2Cbo0yCN_!!*******************-0-rate.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i2/O1CN01t516f32Ka2Ce1gc2f_!!*******************-0-rate.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i3/O1CN01SjClGA2Ka2Cbnztfx_!!*******************-0-rate.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i4/O1CN01zO0X3N2Ka2CdTVRc1_!!*******************-0-rate.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i4/O1CN01Cpns1p2Ka2CcyfZ9o_!!*******************-0-rate.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded//imgextra/i2/O1CN01yhn2v62Ka2CbJa5cR_!!*******************-0-tbbala.jpg", "type": "video", "videoUrl": "//gw.alicdn.com/bao/uploaded//play/u/null/p/1/d/hd/e/6/t/1/521674269783.mp4"}], "mediaSize": "6", "memberLevel": "8", "skuInfo": "机身颜色:黑色钛金属;存储容量:256GB;套餐类型:套餐五[+原快充原线]", "tmallMemberLevel": "4", "userName": "一个超级爱狗狗的男人", "userStarPic": ""}, {"blackCardUserUrl": "//img.alicdn.com/tfs/TB1wrG1elv0gK0jSZKbXXbK2FXa-225-96.png", "content": "从4S开始到现在用了这么多年苹果手机，第一次购买卡贴机,真的完全颠覆我对卡贴机的认识,不要听别人说的这不好那不好的，只有自己用过才知道好不好,收到货后在客服细心指导下几分钟搞定激活,自己买的双卡双待卡贴，5G信号满满,一切功能正常，压根看不出是卡贴机,屏幕专门调的三星的。这家铺子商家靠谱。😄很幸运刚买过第二天价格就上调了几十个大洋。以后有需要还来这家。", "dateTime": "2025-06-22", "feedId": "", "headPic": "//sns.m.taobao.com/avatar/sns/user/flag/sns_logo?type=taobao&kn=wwc_tb_11&bizCode=taobao_avatar&userFlag=RAzN84GK7wS8eNPZLYTRhp4qGymzogth2FQ3jJMngirEHnPKhGq4sznar1BRgUcKJgqzZFP4VnSNP6YiZmiS5Ts4Ltas3cagAFEaQPMg5KnsoduJj3hHWnYCLyR1SQQkXtH2HSTnFCg7k33PcQB5W6g3JsRNH8s2hzaiS6V", "isVip": "true", "media": [{"imageUrl": "//gw.alicdn.com/bao/uploaded/i3/O1CN01WNN9qF1pZLWZV0nK3_!!4611686018427386926-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i1/O1CN01NFREQt1pZLWZBp2jO_!!4611686018427386926-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i1/O1CN019rxY291pZLWZBqaLw_!!4611686018427386926-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i1/O1CN01KWpiav1pZLWZjQQc6_!!4611686018427386926-0-tbbala.jpg", "type": "image", "videoUrl": ""}, {"imageUrl": "//gw.alicdn.com/bao/uploaded/i1/O1CN01ec0bsL1pZLWSnEpf4_!!4611686018427386926-0-tbbala.jpg", "type": "image", "videoUrl": ""}], "mediaSize": "5", "memberLevel": "7", "skuInfo": "机身颜色:白色钛金属;存储容量:256GB;套餐类型:套餐三[US有锁+单卡]", "tmallMemberLevel": "4", "userName": "舊時光", "userStarPic": ""}]}, "keywords": [{"attribute": "20191005-11", "count": "52", "type": "1", "word": "服务态度很好"}, {"attribute": "20051004-11", "count": "19", "type": "1", "word": "使用体验很好"}, {"attribute": "20261000-11", "count": "14", "type": "1", "word": "是正品"}, {"attribute": "20101012-11", "count": "26", "type": "1", "word": "信号很强"}, {"attribute": "20231001-11", "count": "29", "type": "1", "word": "物美价廉"}, {"attribute": "20201000-11", "count": "11", "type": "1", "word": "发货物流快"}], "locatorId": "divisionRate", "pageTitle": "评价", "title": "评价(1000+)"}, "events": {"openFirstItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-Comments_industry", "args": {"feed_id": "", "model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia0": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia1": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openSecondItem": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-Comments_industry", "args": {"feed_id": "", "model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia2": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "keywordsClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-CommentsTag_industry", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "openMedia3": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_newcomments", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}], "titleClick": [{"type": "userTrack", "fields": {"arg1": "Page_Detail_Button_all_industry", "args": {"model": "text"}, "eventId": "2101", "page": "Page_Detail"}}]}}, "detail3AskAll": {"tag": "detail3AskAll", "type": "dinamicx$detail_v3_askall$0$30", "fields": {"group": {"items": [{"answerCountText": "3个回答", "askIcon": "//gw.alicdn.com/imgextra/i4/O1CN01kITAMA1tP2otdGDmR_!!6000000005893-2-tps-72-72.png", "askText": "请问下，16pm有锁双卡信号怎么样？日常聊天，购物扫码支付，有没有问题？谢谢", "askTextColor": "#000000"}, {"answerCountText": "2个回答", "askIcon": "//gw.alicdn.com/imgextra/i4/O1CN01kITAMA1tP2otdGDmR_!!6000000005893-2-tps-72-72.png", "askText": "有买512G的贝贝嘛 想问一下用起来或者打游戏的时候会突然闪退或者关机重启嘛", "askTextColor": "#000000"}]}, "title": "问大家(152)"}, "events": {"itemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}]}}, "detail3UGCDivision": {"tag": "detail3UGCDivision", "type": "dinamicx$detail_v3_part_division$0$12", "fields": {"backgroundColor": "#F3F6F8", "height": "8", "padding": "16"}}, "detail3ShopInfo": {"tag": "detail3ShopInfo", "type": "dinamicx$detail_v3_shop_card$0$17", "fields": {"bgColor": "#F9F9F9", "bgImage": "https://img.alicdn.com/imgextra/i3/15785155/O1CN01yghzNm1nx2J6dsZLr_!!15785155.jpg", "brandIcons": [{"imageUrl": "//gw.alicdn.com/tfs/TB1uyMhisLJ8KJjy0FnXXcFDpXa-132-24.png"}], "certIconConst": "http://gw.alicdn.com/tfs/TB1zgzmlZLJ8KJjy0FnXXcFDpXa-171-148.png", "divisionColor": "#1FFFFFFF", "entranceList": [{"action": "enterShopClick", "text": "进店逛逛", "textColor": "#FFFFFF"}, {"action": "allItemClick", "text": "全部宝贝", "textColor": "#FFFFFF"}], "evaluates": [{"score": "4.9", "scoreTextColor": "#FFFFFF", "title": "宝贝描述", "titleColor": "#B8FFFFFF", "type": "desc"}, {"score": "4.9", "scoreTextColor": "#FFFFFF", "title": "卖家服务", "titleColor": "#B8FFFFFF", "type": "serv"}, {"score": "4.9", "scoreTextColor": "#FFFFFF", "title": "物流服务", "titleColor": "#B8FFFFFF", "type": "post"}], "icon": "https://img.alicdn.com/imgextra/i2/15785155/O1CN017uN8HG1nx2l1cid9c_!!15785155.png", "needMask": "true", "padding": "16", "relationShipUrl": "https://dinamicx.alibabausercontent.com/l_pub/ts_common_subscription_relationship_widget/1684395523233/ts_common_subscription_relationship_widget.zip", "relationShipVersion": "4", "title": "酷酷鱼数码", "titleStyle": {"textColor": "#FFFFFF"}}, "events": {"enterShopClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}, {"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "itemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "brandClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}], "allItemClick": [{"type": "toast", "fields": {"displaytime": "1.5", "fontSize": "16", "message": "信息加载中，客官请稍等", "parentView": "window"}}]}}, "divisionDesc": {"tag": "divisionDesc", "type": "native$division", "fields": {"bizId": "desc", "locatorId": "divisionDesc", "name": "divisionDesc", "payload": {"displayType": "text", "fgcolor": "0x666666", "iconUrl": "", "title": "宝贝详情"}, "type": "division"}}, "detailDescComp": {"tag": "detailDescComp", "type": "native$detaildesc", "fields": {"name": "detailDesc", "payload": {"itemId": "857780853397", "moduleDescParams": {"f": "desc/icoss25613786650841d83fbc7c2baa", "id": "857780853397"}, "shopId": "33278965", "shrinkDesc": "false", "shrinkPriceInfo": "false", "taobaoDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=857780853397&descVersion=7.0&type=0&f=desc/icoss25613786650841d83fbc7c2baa&sellerType=C", "taobaoPcDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=857780853397&descVersion=6.0&type=1&f=icoss!0857780853397!13676346406&sellerType=C", "userId": "15785155"}, "style": "detailDesc", "type": "detailDesc"}, "events": {"actions": [{"type": "ut_exposure", "fields": {"scm": "", "spm": "a2141.7631564.1999077549", "trackPage": "Page_Detail_Show_ProductDetail"}}]}}, "divisionDescRecmdComp": {"tag": "divisionDescRecmdComp", "type": "native$division", "fields": {"bizId": "looked", "locatorId": "divisionDescRecmd", "name": "divisionDescRecmd", "payload": {"bgcolor": "0xf4f4f4", "displayType": "gone", "height": "5", "iconUrl": "//img.alicdn.com/tps/TB1PGyPOVXXXXa8aXXXXXXXXXXX"}, "type": "division"}}, "guessYouLike": {"tag": "guessYouLike", "type": "native$guessyoulike", "fields": {"name": "guessYouLike", "payload": {"api": "mtop.taobao.wireless.home.awesome.itemdetail.recommend", "bizParams": {"m": "detail"}, "channel": "itemdetail", "itemId": "857780853397", "userId": "15785155", "version": "1.0"}, "style": "shopRecommend", "type": "guessYouLike"}, "events": {"actions": [{"type": "ut_exposure", "fields": {"spm": "a2141.7631564.2737766", "trackPage": "Page_Detail_Show_Recommend"}}]}}, "detail3InclusiveBottomBar": {"tag": "detail3InclusiveBottomBar", "type": "dinamicx$detail_v3_inclusive_bottom_bar$0$9", "fields": {"_dObserveStates": {"isCollected": "init"}, "bizId": "bottomBar", "collectSummary": "false", "collected": "false", "detail3": "true", "height": "60", "leftButtons": [{"disabled": "true", "icon": {"color": "#ff5000", "iconFontName": "삊"}, "title": {"color": "#666666", "text": "店铺"}}, {"disabled": "true", "icon": {"color": "#666666", "iconFontName": "쁪"}, "title": {"color": "#666666", "text": "客服"}}, {"disabled": "true", "icon": {"color": "#666666", "iconFontName": "뀚"}, "title": {"color": "#666666", "text": "收藏"}}], "rightButtons": [{"background": {"disabledColor": ["#ffcb00", "#ff9402"], "gradientColor": ["#ffcb00", "#ff9402"]}, "bizCode": "addToCart", "disabled": "true", "title": "加入购物车", "titleStyle": {"color": "#ffffff"}, "widthRatio": "0.5"}, {"background": {"disabledColor": ["#ff7700", "#ff4900"], "gradientColor": ["#ff7700", "#ff4900"]}, "bizCode": "buy", "disabled": "true", "title": "立即购买", "titleStyle": {"color": "#ffffff"}, "widthRatio": "0.5"}]}}, "naviItemLeft": {"tag": "naviItemLeft", "type": "native$detailnaviitem", "fields": {"name": "naviItemLeft", "payload": {"accessHint": "返回", "positionKey": "left", "secondActions": [{"domain": "detail", "type": "go_detail_home"}], "titleSizeRatio": "0.375", "value": "ꁽ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "go_back"}]}}, "naviItemCustom": {"tag": "naviItemCustom", "type": "native$detailnaviitem", "fields": {"name": "naviItemCustom", "payload": {"accessHint": "购物车", "positionKey": "custom", "titleSizeRatio": "0.375", "value": "ꁊ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "user_track", "fields": {"trackName": "ShoppingCart", "trackNamePre": "Button-"}}, {"type": "open_url", "fields": {"url": "https://h5.m.taobao.com/awp/base/cart.htm", "urlParams": {"cartfrom": "detail", "itemId": "857780853397"}}}]}}, "naviItemRight": {"tag": "naviItemRight", "type": "native$detailnaviitem", "fields": {"name": "naviItemRight", "payload": {"accessHint": "更多", "positionKey": "right", "titleSizeRatio": "0.375", "value": "ꁪ"}, "type": "detailNaviItem"}, "events": {"actions": [{"type": "show_menu"}]}}, "naviTabInfo": {"tag": "naviTabInfo", "type": "native$detailnavitabitem", "fields": {"name": "naviTabInfo", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "宝贝"}, "style": "tab", "targetBizId": "header", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "detailInfoAura"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailHome", "trackNamePre": "Button-"}}]}}, "naviTabRate": {"tag": "naviTabRate", "type": "native$detailnavitabitem", "fields": {"name": "naviTabRate", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "pageName": "Page_DetailComments", "secondActions": [{"domain": "detail", "type": "goto_rate_top"}], "title": "评价"}, "style": "tab", "targetBizId": "rate", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionRate"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailRate", "trackNamePre": "Button-"}}, {"type": "ut_exposure", "fields": {"spm": "a2141.7631564.2737664", "trackPage": "Page_Detail_Show_Detail"}}]}}, "naviTabDesc": {"tag": "naviTabDesc", "type": "native$detailnavitabitem", "fields": {"name": "naviTabDesc", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "详情"}, "style": "tab", "targetBizId": "desc", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionDesc"}}, {"type": "user_track", "fields": {"trackName": "GotoDetailDesc", "trackNamePre": "Button-"}}]}}, "naviTabDescRecmd": {"tag": "naviTabDescRecmd", "type": "native$detailnavitabitem", "fields": {"name": "naviTabDescRecmd", "payload": {"iconUrl": "//img.alicdn.com/tps/TB1ZGAeOFXXXXa6XXXXXXXXXXXX", "title": "推荐"}, "style": "tab", "targetBizId": "looked", "type": "detailNaviTabItem"}, "events": {"actions": [{"type": "locator", "fields": {"locatorId": "divisionDescRecmd"}}, {"type": "user_track", "fields": {"trackName": "GotoShopRecommend", "trackNamePre": "Button-"}}]}}, "detail3NaviItemBack": {"tag": "detail3NaviItemBack", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemBack", "payload": {"accessHint": "返回", "positionKey": "back", "value": "ꁽ"}, "type": "detailNaviItem"}}, "detail3NaviItemSearch": {"tag": "detail3NaviItemSearch", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemSearch", "payload": {"accessHint": "搜索", "positionKey": "search", "rightIndex": "0", "value": "끺"}, "type": "detailNaviItem"}}, "detail3NaviItemShare": {"tag": "detail3NaviItemShare", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemShare", "payload": {"accessHint": "分享", "positionKey": "share", "rightIndex": "1", "value": "ꄪ"}, "type": "detailNaviItem"}}, "detail3NaviItemCart": {"tag": "detail3NaviItemCart", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemCart", "payload": {"accessHint": "购物车", "jumpUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "positionKey": "cart", "rightIndex": "2", "value": "ꁊ"}, "type": "detailNaviItem"}}, "detail3NaviItemMore": {"tag": "detail3NaviItemMore", "type": "native$detailnaviitem", "fields": {"name": "detail3NaviItemMore", "payload": {"accessHint": "更多", "positionKey": "more", "rightIndex": "4", "value": "ꁪ"}, "type": "detailNaviItem"}}, "detail3ElevatorAnchor": {"tag": "detail3ElevatorAnchor", "type": "native$detailelevatorfloat", "fields": {"data": {"expand": "true", "height": "133", "marginBottom": "122", "selectedLocationId": "detailInfoAura", "titles": [{"locationId": "detailInfoAura", "title": "宝贝"}, {"locationId": "divisionRate", "title": "评价"}, {"locationId": "divisionDesc", "title": "详情"}, {"locationId": "divisionDescRecmd", "title": "推荐"}], "width": "48"}, "template": {"name": "detail_v3_elevator_anchor", "url": "https://dinamicx.alibabausercontent.com/pub/detail_v3_elevator_anchor/1693993346457/detail_v3_elevator_anchor.zip", "version": "16"}, "type": "detail3ElevatorAnchor"}}}, "linkage": {"common": {"compress": "true"}, "signature": "53176a83d6c8a8606a10b089fe40d2b0"}, "hierarchy": {"root": "detail4", "structure": {"detail4": ["detailHome", "bottomBar", "naviBar", "ttNaviBar", "ttFloat"], "detailHome": ["detailInfoAura", "divisonDesc", "detailDesc", "divisionDescRecmd", "descRecmd"], "detailInfoAura": ["detailHeaderPic", "detail4SkuPickerSection", "detail4TopLineSection", "detail4PriceSection", "detail4TitleSection", "detail4FirstDividingLineSection", "detail4ArgumentSection", "detail4ParamsSection", "detail4InfoAndCommentDivider", "detail3CommentSection", "detail3AskAllSection", "detail3UGCDivisionSection", "detail4ShopSection"], "detailHeaderPic": ["headerPic"], "headerPic": ["mainContainer"], "mainContainer": ["mainFrame", "progressArea", "locatorArea"], "mainFrame": ["frameGroup_video", "frameGroup_image"], "frameGroup_video": ["frame_video_0"], "frame_video_0": ["frameVideo_video_0"], "frameGroup_image": ["frame_pic_0", "frame_pic_1", "frame_pic_2", "frame_pic_3", "frame_pic_4"], "frame_pic_0": ["frameImage_pic_0"], "frame_pic_1": ["frameImage_pic_1"], "frame_pic_2": ["frameImage_pic_2"], "frame_pic_3": ["frameImage_pic_3"], "frame_pic_4": ["frameImage_pic_4"], "progressArea": ["progressBar"], "locatorArea": ["locatorBar"], "locatorBar": ["locatorItem_video", "locatorItem_image"], "detail4SkuPickerSection": ["detail4SkuPicker"], "detail4TopLineSection": ["detail4TopLine"], "detail4PriceSection": ["detail4Price"], "detail4TitleSection": ["detail4Title"], "detail4FirstDividingLineSection": ["detail4FirstDividingLine"], "detail4ArgumentSection": ["detail4ArgumentTopLine"], "detail4ParamsSection": ["detail4ParamsDefault"], "detail4InfoAndCommentDivider": ["detail4ItemInfoAndCommentDivider"], "detail3CommentSection": ["detail3Comment"], "detail3AskAllSection": ["detail3AskAll"], "detail3UGCDivisionSection": ["detail3UGCDivision"], "detail4ShopSection": ["detail3ShopInfo"], "divisonDesc": ["divisionDesc"], "detailDesc": ["detailDescComp"], "divisionDescRecmd": ["divisionDescRecmdComp"], "descRecmd": ["guessYouLike"], "bottomBar": ["detail3BottomBarSection"], "detail3BottomBarSection": ["detail3InclusiveBottomBar"], "naviBar": ["naviControl", "naviTabs"], "naviControl": ["naviItemLeft", "naviItemCustom", "naviItemRight"], "naviTabs": ["naviTabInfo", "naviTabRate", "naviTabDesc", "naviTabDescRecmd"], "ttNaviBar": ["detail3NaviItemBack", "detail3NaviItemSearch", "detail3NaviItemShare", "detail3NaviItemCart", "detail3NaviItemMore"], "ttFloat": ["detail3ElevatorAnchor"]}}, "endpoint": {"ultronage": "true", "protocolVersion": "4.0", "contextVersion": "taoDetailIndustry_gc_202506201635358962_328766", "domainCode": "taoDetailIndustry", "page": "detail4", "traceIds": ["213e037817509245757488436e1c4e"], "meta": {"template": {"id": "detail4$taoDetailIndustry_gc_202506201635358962_328766", "version": "6e2b0746b3178cde14fe548a254a9ec1", "cacheFields": ["container"]}}, "features": "2"}, "reload": "true", "components": {"native$headerbgimage": {"name": "headerbgimage", "type": "native", "url": "", "version": "0"}, "native$headervideo": {"name": "headervideo", "type": "native", "url": "", "version": "0"}}, "model": {"headerPic": {"dragEnd": {"events": {"dragEvent": [{"type": "locatorTo", "fields": {"locatorId": "desc"}}, {"type": "userTrack", "fields": {"arg1": "Page_Detail_Button-ItemDetailRecommend", "args": {}, "eventId": "2201", "page": "Page_Detail"}}]}, "fields": {"position": "right", "pullText": "滑\n动\n发\n现\n更\n多\n内\n容", "releaseText": "释\n放\n发\n现\n更\n多\n内\n容"}}, "feature": {"disableSyncPropWithSku": "true"}, "groups": [{"id": "frameGroup_image", "items": [{"content": {"fields": {"code": "video_0", "contentStyle": {"paddingBottom": "0"}, "subType": "override", "thumbnailDimension": "1:1", "thumbnailUrl": "http://img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg", "type": "video", "videoId": "520265096873", "videoUrl": "http://cloud.video.taobao.com/play/u/15785155/p/2/e/6/t/1/520265096873.mp4?appKey=38829"}, "id": "frameVideo_video_0", "type": "native$headervideo"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_video_0", "tagName": "宝贝讲解"}, {"content": {"fields": {"type": "image", "code": "pic_0", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg"}, "id": "frameImage_pic_0", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_0"}, {"content": {"fields": {"type": "image", "code": "pic_1", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i4/15785155/O1CN015pkcPr1nx2jVWa0CP_!!15785155.jpg"}, "id": "frameImage_pic_1", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_1"}, {"content": {"fields": {"type": "image", "code": "pic_2", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i2/15785155/O1CN01dwgDra1nx2jXXPgMe_!!15785155.jpg"}, "id": "frameImage_pic_2", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_2"}, {"content": {"fields": {"type": "image", "code": "pic_3", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/15785155/O1CN019R5rm91nx2jWoBsXY_!!15785155.jpg"}, "id": "frameImage_pic_3", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_3"}, {"content": {"fields": {"type": "image", "code": "pic_4", "contentStyle": {"paddingBottom": "0"}, "url": "http://img.alicdn.com/imgextra/i1/15785155/O1CN01xDaHb01nx2jXcYJ4s_!!15785155.jpg"}, "id": "frameImage_pic_4", "type": "native$headerbgimage"}, "dimension": "1:1", "enableLightOff": "false", "id": "frame_pic_4"}], "locatorId": "image"}], "locators": [{"id": "image", "name": "图集"}], "mainPicBizData": {"rbAction": {"template": {"name": "detail_v4_gallery_mini_action", "version": "5", "url": "https://dinamicx.alibabausercontent.com/l_pub/detail_v4_gallery_mini_action/1749436254191/detail_v4_gallery_mini_action.zip"}}}, "properties": {"stretchHeight": "58"}, "smallWindow": []}}, "global": {"data": {"bizData": {"useClientEngine": "true"}, "debug": {"fromStatic": "true", "host": "taodetail033040050011.unsh.ea119@***********", "industryTraceInfo": [], "traceId": "213e037817509245757488436e1c4e"}, "detailClientData": {"detailClientOpt": {"guessYouLikeServerless": "true", "groupId": "0"}}, "detailOpt": {"cacheTimeToLive": "14400", "detailVersion": "industry"}, "diversion": {"detailTopSearch": {"url": "https://s.m.taobao.com/h5entry?g_channelSrp=detail&placeholder=&showText=搜索宝贝&g_historyOn=true&g_csearchdoor_spm=a2141.13130650&launchMode=android_new_task"}}, "feature": {"isPadDevice": "false", "enableDiscountAnim": "true", "headerPicScrollCycle": "false", "industryMainPic": "true", "maskingProduct": "false", "finalUltron": "true", "guessYouLikeNewStyle": "true", "delMainPicEvent": "true", "detail3VideoAutoPlay": "false", "commonHiddenItemRecmd": "false", "multiMerchantProduct": "false", "titleItemTagMix": "true", "hintInsideCartButton": "false", "enablePriceAnim": "true", "detail3": "true", "preloadDetail": "true", "detail3HeadPic": "true", "forceNaviAlpha": "true", "mainPicHideLocator": "true", "hintDetail3BottomBar": "true", "descNewStyle": "true"}, "ignore": {"ignoreAll": "true"}, "item": {"containerDimension": "1:1", "images": ["https://img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg", "https://img.alicdn.com/imgextra/i4/15785155/O1CN015pkcPr1nx2jVWa0CP_!!15785155.jpg", "https://img.alicdn.com/imgextra/i2/15785155/O1CN01dwgDra1nx2jXXPgMe_!!15785155.jpg", "https://img.alicdn.com/imgextra/i1/15785155/O1CN019R5rm91nx2jWoBsXY_!!15785155.jpg", "https://img.alicdn.com/imgextra/i1/15785155/O1CN01xDaHb01nx2jXcYJ4s_!!15785155.jpg"], "title": "【16promax】Apple/苹果 iPhone 16 Pro Max 国行US版全网通手机", "videos": [{"spatialVideoDimension": "1:1", "type": "2", "url": "https://cloud.video.taobao.com/play/u/15785155/p/2/e/6/t/1/520265096873.mp4?appKey=38829", "videoId": "520265096873", "videoThumbnailURL": "https://img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg", "weexRecommendUrl": "https://market.m.taobao.com/apps/market/detailrax/recommend-items.html?spm=a2116h.app.0.0.16d957e9U2bxVj&wh_weex=true&itemId=857780853397"}]}, "pageLayout": {}, "trade": {"cartJumpUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "isBanSale4Oversea": "false", "isWap": "false", "useWap": "false"}, "useClientEngine": "true"}}}, "name": "esi"}], "feature": {"isOnLine": "true", "tcloudToH5": "true"}, "item": {"brandValueId": "30111", "businessId": "default", "cartUrl": "https://h5.m.taobao.com/awp/base/cart.htm", "categoryId": "1512", "commentCount": "0", "countMultiple": [], "exParams": {}, "favcount": "13617", "h5ItemUrl": "https://new.m.taobao.com/detail.htm?id=857780853397&hybrid=true", "h5moduleDescUrl": "//mdetail.tmall.com/templates/pages/itemDesc?id=857780853397", "images": ["//img.alicdn.com/imgextra/i4/15785155/O1CN01rO4k5E1nx2lCO3HuU_!!15785155.jpg", "//img.alicdn.com/imgextra/i4/15785155/O1CN015pkcPr1nx2jVWa0CP_!!15785155.jpg", "//img.alicdn.com/imgextra/i2/15785155/O1CN01dwgDra1nx2jXXPgMe_!!15785155.jpg", "//img.alicdn.com/imgextra/i1/15785155/O1CN019R5rm91nx2jWoBsXY_!!15785155.jpg", "//img.alicdn.com/imgextra/i1/15785155/O1CN01xDaHb01nx2jXcYJ4s_!!15785155.jpg"], "itemId": "857780853397", "moduleDescParams": {"f": "desc/icoss25613786650841d83fbc7c2baa", "id": "857780853397"}, "moduleDescUrl": "//hws.m.taobao.com/d/modulet/v5/WItemMouldDesc.do?id=857780853397&f=icoss!0857780853397!***********", "openDecoration": "false", "pcADescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=857780853397&descVersion=7.0&type=1&f=desc/icoss25613786650841d83fbc7c2baa&sellerType=C", "rootCategoryId": "1512", "skuText": "请选择机身颜色 套餐类型 存储容量 ", "taobaoDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=857780853397&descVersion=7.0&type=0&f=desc/icoss25613786650841d83fbc7c2baa&sellerType=C", "taobaoPcDescUrl": "//market.m.taobao.com/app/detail-project/desc/index.html?id=857780853397&descVersion=6.0&type=1&f=icoss!0857780853397!13676346406&sellerType=C", "title": "【16promax】Apple/苹果 iPhone 16 Pro Max 国行US版全网通手机", "tmallDescUrl": "//mdetail.tmall.com/templates/pages/desc?id=857780853397"}, "preload": "true"}}}, "ret": ["SUCCESS::调用成功"], "v": "1.0"}