import time

import mitmproxy.http
import pymongo
from mitmproxy import http
import re
import json
import datetime

import redis

# 海尔洗衣机旗舰店和美的官方旗舰店
# 美的小家电旗舰店
class CaptureInfoWriteFile:
    def __init__(self):
        # 预设关键词，可根据业务需要调整
        self.keyword = "美的官方旗舰店"
        
        self.client = pymongo.MongoClient("**************************************************", compressors='zstd')
        # 创建淘宝抓包数据库和数据表
        self.db = self.client["douyin"]
        self.collection = self.db["douyin_sp_07_26_meide"]

    def request(self, flow: mitmproxy.http.HTTPFlow):
        if 'mtop' in flow.request.url:
            print(flow.request.url)
            flow.request.headers['Accept-Encoding'] = 'gzip'
    def response(self, flow: mitmproxy.http.HTTPFlow):
        # pass
        if 'https://ecom5-normal-m.ecombdapi.com/shop/bff/tab/?sec_shop_id' in flow.request.url and "'perf_log_extra': {'anchor_tab_id': '3', 'auto_render': '0', 'refactor_api': 'v3'," in str(flow.response.json()):
            data = flow.response.json()
            print('shop/bff/tab：', str(data)[:500])
            data["inserted_at"] = datetime.date.today().isoformat()
            # 将关键词一并写入数据库
            data["keyword"] = self.keyword
            self.collection.insert_one(data)
        if 'https://ecom5-normal-m.ecombdapi.com/aweme/v1/store/product/bff/?request_tab_type' in flow.request.url and "{'biz_extra': {'__PRODUCTS_SECTION__': '{" in str(flow.response.json()):
            data = flow.response.json()
            print('aweme/v1/store/product/bff：', str(data)[:500])
            data["inserted_at"] = datetime.date.today().isoformat()
            # 将关键词一并写入数据库
            data["keyword"] = self.keyword
            self.collection.insert_one(data)



addons = [CaptureInfoWriteFile()]

