import json
import ast
from pathlib import Path


def extract_products(file_path: str):
    # 测试数据.json 是一个 Python 字面量格式（单引号），先整体读取后用 ast.literal_eval 解析
    text = Path(file_path).read_text(encoding="utf-8", errors="ignore")

    try:
        data = json.loads(text)
    except json.JSONDecodeError:
        # 回退到 ast.literal_eval 处理单引号/非标准 JSON
        data = ast.literal_eval(text)

    # 直接将每条记录序列化为标准 JSON 字符串返回
    return [json.dumps(item, ensure_ascii=False) for item in data]


def main():
    file_path = "测试数据.json"
    products_json = extract_products(file_path)
    for idx, record in enumerate(products_json, 1):
        print(f"{idx}. {record}")


if __name__ == "__main__":
    main() 