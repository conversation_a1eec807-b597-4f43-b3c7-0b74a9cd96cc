import pymongo
from typing import Optional, Dict, Any

class DataReader:
    def __init__(self):
        # 连接MongoDB数据库
        self.client = pymongo.MongoClient("**************************************************")
        # 创建抖音数据库和数据表
        self.db = self.client["pinduoduo"]
        self.collection = self.db["shop_goods_list_20250725_v2"]
    
    def get_one_data(self) -> Optional[Dict[str, Any]]:
        """获取一条数据
        
        Returns:
            Optional[Dict[str, Any]]: MongoDB文档对象,包含以下字段:
            {
                "_id": ObjectId,  # MongoDB文档ID
                "aweme_id": str,  # 视频ID
                "desc": str,      # 视频描述
                "author": dict,   # 作者信息
                "statistics": dict, # 统计数据
                ...其他字段
            }
        """
        try:
            # 获取第一条数据并打印完整内容
            data = self.collection.find().limit(1).next()
            if data:
                print("获取到的数据内容:")
                print(f"数据类型: {type(data)}")
                print(f"包含的字段: {list(data.keys())}")
                print("数据详情:")
                for key, value in data.items():
                    print(f"{key}: {value}")
                return data
            else:
                print("数据表中没有数据")
                return None
        except Exception as e:
            print(f"获取数据时出错: {e}")
            return None
    
    def close_connection(self):
        """关闭数据库连接"""
        self.client.close()
        print("数据库连接已关闭")

if __name__ == "__main__":
    # 创建数据读取器实例
    reader = DataReader()
    
    # 获取一条数据
    data = reader.get_one_data()
    
    # 关闭连接
    reader.close_connection()